<template>
  <TDrawer
    :title="getDialogTitle()"
    :visible.sync="show"
    size="28%"
    class="add-dialog"
    @close="cancel"
  >
    <el-form
      ref="formRef"
      :rules="rules"
      :model="form"
      size="small"
      label-position="right"
      label-width="110px"
    >
      <el-form-item label="上级分类" v-if="!isRootCategory">
        <el-input
          v-model.trim="categoryParent"
          disabled
          :placeholder="
            type === 'edit' ? '上级分类（编辑时不可修改）' : '上级分类'
          "
        />
      </el-form-item>
      <el-form-item label="目录分类名称" prop="name">
        <el-input
          v-model.trim="form.name"
          :maxlength="50"
          :disabled="type === 'view'"
          clearable
          show-word-limit
          placeholder="请输入目录分类名称"
        />
      </el-form-item>
      <el-form-item
        label="分类编码"
        prop="preCode"
        v-if="type === 'add' && isRootCategory"
      >
        <el-input
          v-model.trim="form.preCode"
          :maxlength="20"
          clearable
          show-word-limit
          placeholder="请输入分类编码（支持英文字母和数字）"
        />
      </el-form-item>
      <el-form-item label="分类编码" v-if="type === 'add' && !isRootCategory">
        <el-input
          v-model.trim="form.preCode"
          :maxlength="20"
          disabled
          show-word-limit
          placeholder="分类编码（通过接口自动生成）"
        />
      </el-form-item>
      <el-form-item label="分类编码" v-if="type === 'edit' || type === 'view'">
        <el-input
          v-model.trim="form.preCode"
          :maxlength="20"
          disabled
          show-word-limit
          placeholder="分类编码（编辑时不可修改）"
        />
      </el-form-item>
      <el-form-item label="是否上线" prop="isEnabled">
        <el-select
          v-model="form.isEnabled"
          :disabled="type === 'view' || type === 'edit'"
          placeholder="请选择是否上线"
          style="width: 100%"
        >
          <el-option label="是" value="1" />
          <el-option label="否" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="排序">
        <el-input-number
          v-model.trim="form.sort"
          :disabled="type === 'view'"
          controls-position="right"
          :precision="0"
          :min="1"
          :max="9999"
          style="width: 100%"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="流程图">
        <el-upload
          class="image-uploader"
          action="#"
          accept="image/jpeg,image/jpg,image/png"
          :show-file-list="false"
          :http-request="customUpload"
          :before-upload="beforeImageUpload"
          :disabled="type === 'view'"
        >
          <img v-if="imageUrl" :src="imageUrl" class="uploaded-image" />
          <i v-else class="el-icon-plus image-uploader-icon"></i>
        </el-upload>
        <div class="upload-tip">支持JPG、JPEG、PNG格式，大小不超过2MB</div>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          v-model.trim="form.remark"
          :disabled="type === 'view'"
          type="textarea"
          :rows="3"
          :maxlength="250"
          :autosize="{ minRows: 6, maxRows: 6 }"
          show-word-limit
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button size="mini" class="cancel-btn" @click="cancel">
        {{ type === 'view' ? '关闭' : '取消' }}
      </el-button>
      <el-button
        v-if="type !== 'view'"
        class="save-btn"
        type="primary"
        size="mini"
        :loading="okLoading"
        @click="submit"
      >
        确定
      </el-button>
    </template>
  </TDrawer>
</template>

<script setup>
import {
  ref,
  reactive,
  computed,
  nextTick,
  getCurrentInstance,
  defineEmits,
  defineExpose
} from 'vue'
import TDrawer from 'biz/components/common/t-drawer'
import { uploadFile } from 'biz/http/api'

const { proxy } = getCurrentInstance()

// 定义emits
const emit = defineEmits(['update'])

// 响应式数据
const show = ref(false)
const okLoading = ref(false)
const categoryParent = ref('')
const type = ref('')
const parentInfo = ref(null) // 父级分类信息
const imageUrl = ref('') // 图片预览URL

// 表单数据
const form = reactive({
  id: '',
  preCode: '', // 分类编码
  name: '', // 分类名称
  sort: 0, // 排序
  remark: '', // 备注
  isEnabled: 1, // 是否上线 0下线 1上线
  imageId: '', // 图片上传之后的ID
  pid: '' // 选择指定父级节点下创建时传入
})

// 表单验证规则
const rules = ref({})

// 动态更新验证规则
const updateRules = () => {
  const baseRules = {
    name: [
      { required: true, message: '请输入目录分类名称', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (value && checkNameDuplicate(value, form.id)) {
            callback(new Error('分类名称已存在，请重新输入'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ],
    isEnabled: [
      { required: true, message: '请选择是否上线', trigger: 'change' }
    ]
  }

  if (type.value === 'add') {
    // 根据是否为顶级分类设置不同的验证规则
    if (isRootCategory.value) {
      // 顶级分类需要手动输入编码，设置必填验证
      baseRules.preCode = [
        { required: true, message: '请输入分类编码', trigger: 'change' },
        {
          pattern: /^[A-Za-z0-9]+$/,
          message: '分类编码只能包含英文字母和数字',
          trigger: 'change'
        },
        {
          validator: (rule, value, callback) => {
            if (value && checkCodeDuplicate(value, form.id)) {
              callback(new Error('分类编码已存在，请重新输入'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    } else {
      // 下级分类自动生成编码，不需要必填验证，但需要格式验证
      baseRules.preCode = [
        {
          pattern: /^[A-Za-z0-9]+$/,
          message: '分类编码只能包含英文字母和数字',
          trigger: 'change'
        },
        {
          validator: (rule, value, callback) => {
            if (value && checkCodeDuplicate(value, form.id)) {
              callback(new Error('分类编码已存在，请重新输入'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
        }
      ]
    }
  } else if (type.value === 'edit') {
    baseRules.preCode = [
      { required: true, message: '分类编码不能为空', trigger: 'change' },
      {
        pattern: /^[A-Za-z0-9]+$/,
        message: '分类编码只能包含英文字母和数字',
        trigger: 'change'
      },
      {
        validator: (rule, value, callback) => {
          if (value && checkCodeDuplicate(value, form.id)) {
            callback(new Error('分类编码已存在，请重新输入'))
          } else {
            callback()
          }
        },
        trigger: 'blur'
      }
    ]
  }

  rules.value = baseRules

  // 在下一个tick更新表单验证
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// 表单引用
const formRef = ref(null)

// 计算属性
const isRootCategory = computed(() => {
  return !parentInfo.value
})

const categoryLevel = computed(() => {
  if (!parentInfo.value) return 1 // 一级分类

  // 简单的层级判断：有父级就是子级
  return 2 // 有父级分类则当前是子级分类
})

// 获取弹窗标题
const getDialogTitle = () => {
  const operationMap = {
    add: '新增分类',
    edit: '编辑分类',
    view: '查看分类'
  }
  return operationMap[type.value] || '新增分类'
}

// 方法定义
const cancel = () => {
  show.value = false
  okLoading.value = false
  categoryParent.value = ''
  parentInfo.value = null
  imageUrl.value = '' // 清理图片预览
  for (const k in form) {
    if (k === 'isEnabled') {
      form[k] = 1
    } else if (k === 'sort') {
      form[k] = 0
    } else {
      form[k] = ''
    }
  }
  formRef.value.clearValidate()
}

// 生成分类编码
const generateCode = async (parentInfo) => {
  if (isRootCategory.value) {
    // 一级分类手动输入
    form.preCode = ''
  } else {
    // 下级分类通过接口自动生成
    try {
      const res = await proxy.$api.bizApi.assetClassify.generateCode({
        pid: parentInfo.id
      })

      if (res.code === '200' && res.data) {
        form.preCode = res.data
      } else {
        proxy.$message.error(res.message || '生成分类编码失败')
        // 如果接口失败，使用默认编码
        form.preCode = `${parentInfo.preCode}001`
      }
    } catch (error) {
      console.error('生成分类编码失败:', error)
      proxy.$message.error('生成分类编码失败，请稍后重试')
      // 出错时使用默认编码
      form.preCode = `${parentInfo.preCode}001`
    }
  }
}

// 检查分类名称是否重复（简化版本，实际应该调用后端接口验证）
const checkNameDuplicate = (name, currentId = null) => {
  // 这里可以调用后端接口验证名称是否重复
  // 暂时返回false，表示不重复
  return false
}

// 检查分类编码是否重复（简化版本，实际应该调用后端接口验证）
const checkCodeDuplicate = (code, currentId = null) => {
  // 这里可以调用后端接口验证编码是否重复
  // 暂时返回false，表示不重复
  return false
}

// 自定义上传方法
const customUpload = async (options) => {
  let formData = {}
  formData.multipartFile = options.file
  formData.fileName = options.file.name
  formData.code = 'ASSET_01'
  try {
    const res = await uploadFile(formData)
    if (res.code === '200' && res.data) {
      // 保存上传返回的attachId
      form.imageId = res.data.attachId

      // 使用统一的API预览地址
      imageUrl.value = `/api/${config.appCode}/attachment/preview?attachId=${res.data.attachId}`

      proxy.$message.success('图片上传成功')
    } else {
      proxy.$message.error(res.message || '图片上传失败')
    }
  } catch (error) {
    console.error('图片上传失败:', error)
    proxy.$message.error('图片上传失败，请稍后重试')
  }
}

// 图片上传前的校验
const beforeImageUpload = (file) => {
  // 支持的图片格式
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
  const isValidType = allowedTypes.includes(file.type)
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isValidType) {
    proxy.$message.error('上传图片只能是 JPG、JPEG、PNG 格式!')
    return false
  }
  if (!isLt2M) {
    proxy.$message.error('上传图片大小不能超过 2MB!')
    return false
  }

  // 检查文件名后缀（双重验证）
  const fileName = file.name.toLowerCase()
  const validExtensions = ['.jpg', '.jpeg', '.png']
  const hasValidExtension = validExtensions.some((ext) =>
    fileName.endsWith(ext)
  )

  if (!hasValidExtension) {
    proxy.$message.error('文件扩展名必须是 .jpg、.jpeg 或 .png!')
    return false
  }

  return true
}

const open = (openType, infoP, infoC) => {
  type.value = openType
  show.value = true

  // 重置表单数据
  imageUrl.value = '' // 清理图片预览
  for (const k in form) {
    if (k === 'isEnabled') {
      form[k] = 1
    } else if (k === 'sort') {
      form[k] = 0
    } else {
      form[k] = ''
    }
  }

  // 设置父级分类信息
  parentInfo.value = infoP

  if (openType === 'add') {
    // 新增操作
    if (!infoP) {
      // 没有父级分类，新增一级分类
      categoryParent.value = ''
      form.pid = ''
    } else {
      // 有父级分类，新增子分类
      categoryParent.value = infoP.name
      form.pid = infoP.id

      // 自动生成编码
      generateCode(infoP)
    }
  } else {
    // 编辑或查看操作
    if (infoP) {
      // 有父级分类信息，说明是子分类
      categoryParent.value = infoP.name
      form.pid = infoP.id
      parentInfo.value = infoP
    } else {
      // 没有父级分类信息，说明是一级分类
      categoryParent.value = ''
      form.pid = ''
      parentInfo.value = null
    }
  }

  // 更新验证规则
  updateRules()

  if (openType === 'edit' || openType === 'view') {
    const editData = infoC || infoP // 兼容不同的调用方式

    if (editData) {
      // 直接使用传入的数据回显
      for (const k in form) {
        if (editData[k] !== undefined) {
          form[k] = editData[k]
        }
      }

      // 字段已经统一，无需映射

      // 回显图片 - 使用与列表页相同的预览逻辑
      if (editData.imageId) {
        // 使用统一的API预览地址
        imageUrl.value = `/api/${config.appCode}/attachment/preview?attachId=${editData.imageId}`
      } else {
        imageUrl.value = ''
      }
    }
  }

  nextTick(() => {
    formRef.value.clearValidate()
  })
}
const submit = () => {
  // 查看模式不允许提交
  if (type.value === 'view') {
    return
  }

  formRef.value.validate((valid) => {
    if (valid) {
      okLoading.value = true

      // 根据操作类型准备不同的API参数
      let apiParams
      let apiCall

      if (type.value === 'add') {
        // 新增时传递所有字段
        apiParams = {
          preCode: form.preCode,
          name: form.name,
          sort: form.sort,
          remark: form.remark,
          isEnabled: form.isEnabled,
          imageId: form.imageId || '',
          pid: form.pid || ''
        }
        console.log('调用新增资产分类接口，参数：', apiParams)
        apiCall = proxy.$api.bizApi.assetClassify.addAssetsClassify(apiParams)
      } else if (type.value === 'edit') {
        // 编辑时传递所有字段（包括不可修改的字段）
        apiParams = {
          id: form.id,
          preCode: form.preCode, // 编辑时需要传递，但前端不允许修改
          name: form.name,
          sort: form.sort,
          remark: form.remark,
          isEnabled: form.isEnabled, // 编辑时需要传递，但前端不允许修改
          imageId: form.imageId || '',
          pid: form.pid || '' // 编辑时需要传递，但前端不允许修改
        }
        console.log('调用编辑资产分类接口，参数：', apiParams)
        apiCall = proxy.$api.bizApi.assetClassify.editAssetsClassify(apiParams)
      }

      // 执行API调用
      apiCall
        .then((res) => {
          if (res.code === '200') {
            cancel()
            const operationText = type.value === 'add' ? '新增' : '编辑'
            proxy.$notify({
              title: '操作成功',
              message: `${operationText}分类成功`,
              type: 'success',
              duration: 2000
            })
            // 通知父组件更新树和列表
            emit('update')
            emit('updateTree')
          } else {
            proxy.$message.error(res.message || '操作失败')
            okLoading.value = false
          }
        })
        .catch((error) => {
          console.error('API调用失败:', error)
          proxy.$message.error('网络错误，请稍后重试')
          okLoading.value = false
        })
    }
  })
}

// 暴露方法给父组件
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.add-dialog {
  /deep/ .el-drawer {
    min-width: 430px;
  }
}

/deep/ .el-input--suffix .el-input__inner {
  padding-right: 80px;
}
//禁用/下拉重置
/deep/ .el-select .el-input--suffix .el-input__inner,
/deep/ .is-disabled .el-input__inner {
  padding-right: 30px;
}

// 字段提示信息样式
.field-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

// 图片上传样式
.image-uploader {
  /deep/ .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    width: 120px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: #409eff;
    }
  }

  .image-uploader-icon {
    font-size: 28px;
    color: #8c939d;
  }

  .uploaded-image {
    width: 120px;
    height: 80px;
    object-fit: cover;
    display: block;
  }
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}
</style>
