<template>
  <el-form
    ref="formRef"
    :model="formState"
    size="small"
    label-width="140px"
    :rules="rules"
    :disabled="isOnlyRead"
  >
    <TitleInfo
      style="width: 100%"
      title="基本属性"
      :animation="true"
      v-model="titleActiveA"
    >
      <template #content>
        <el-row type="flex" style="flex-wrap: wrap">
          <el-col :span="8">
            <el-form-item label="资产分类" prop="assetCategory">
              <SelectPlus
                dictType="BM_ASSET_CATEGORY"
                v-model="formState.assetCategory"
                clearable
                style="width: 100%"
                placeholder="请选择资产分类"
                @change="handleAssetCategoryChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产编码" prop="assetCode">
              <el-input
                v-model="formState.assetCode"
                maxlength="50"
                placeholder="资产编码"
                readonly
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="资产类型" prop="assetType">
              <SelectPlus
                dictType="BM_ASSET_TYPE"
                v-model="formState.assetType"
                clearable
                style="width: 100%"
                placeholder="请选择资产类型"
              />
            </el-form-item>
          </el-col>

          <el-col :span="8" v-if="isRelatedDir">
            <el-form-item label="关联目录" prop="directoryName">
              <div style="display: flex; gap: 0px">
                <el-input
                  v-model="formState.directoryName"
                  maxlength="50"
                  @focus="handleOpen"
                  readonly
                  :disabled="operationType === 'change' || isOnlyRead"
                  placeholder="请选择关联目录"
                />
                <el-button
                  v-if="operationType !== 'change'"
                  type="primary"
                  @click="clearDirectory"
                >
                  清除
                </el-button>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="数据源" prop="dsCode">
              <div style="display: flex; gap: 8px">
                <el-select
                  v-model="formState.dsCode"
                  :placeholder="isOnlyRead ? '' : '请选择数据源'"
                  style="width: 100%"
                  @change="changeDs"
                  filterable
                >
                  <el-option
                    v-for="(item, index) in dataSourceList"
                    :key="index"
                    :value="item.value"
                    :label="item.text"
                  ></el-option>
                </el-select>
                <el-button
                  v-if="formState.dsCode"
                  type="primary"
                  size="mini"
                  title="触发数据源采集，获取数据源下最新的物理表"
                  @click="handleCountDataSource"
                >
                  采集数据源
                </el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="物理表名" prop="tableName">
              <el-select
                v-model="formState.tableName"
                placeholder="请选择物理表名"
                style="width: 100%"
                @change="changeTableName"
                filterable
              >
                <el-option
                  v-for="(item, index) in dsTableList"
                  :key="index"
                  :value="item"
                  :label="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="formState.tableName === '其他'">
            <el-form-item
              label="其他物理表名"
              prop="otherTableName"
              style="width: 100%; margin-bottom: 36px"
            >
              <el-input
                v-model.trim="formState.otherTableName"
                :maxlength="tableNameMaxLength"
                placeholder="请输入物理表名"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="表中文名" prop="tableNameCn">
              <el-input
                v-model.trim="formState.tableNameCn"
                maxlength="50"
                placeholder="请输入表中文名"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row type="flex" style="flex-wrap: wrap">
          <el-col :span="8" v-if="resOpenTypeSet">
            <el-form-item label="开放类型" prop="openType">
              <SelectPlus
                dictType="BM_OPEN_TYPE"
                v-model="formState.openType"
                clearable
                style="width: 100%"
                placeholder="请选择开放类型"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-auth="['switch_mychain_deposit']">
            <el-form-item label="是否上链" prop="depositFlag">
              <template #label>
                是否上链
                <el-popover
                  placement="bottom"
                  width="200"
                  trigger="hover"
                  content="选是，资源挂接记录将上区块链保存，上链后可查看该上链证书"
                >
                  <template #reference>
                    <i class="el-icon-warning-outline" />
                  </template>
                </el-popover>
              </template>
              <el-radio-group
                v-model="formState.depositFlag"
                @input="handleChangeDepositFlag"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="抽取方式" prop="tableExtractType">
              <template #label>
                抽取方式
                <el-popover placement="bottom" width="200" trigger="hover">
                  <div>
                    时间戳抽取：
                    该模式适合有增量标识的表，抽取都会基于增量字段值增量获取数据。
                  </div>
                  <div>
                    全量抽取：全量抽取每次运行都会将表数据重新全部抽取，适合没有增量标识的表数据抽取。
                  </div>
                  <div>
                    触发器抽取：当勾选了是否第一次全抽则任务运行时会先全量抽取一遍数据，然后在从触发器变动记录表获取抽取数据。适用与没有增量标识且有物理删除操作的表。
                  </div>
                  <template #reference>
                    <i class="el-icon-warning-outline" />
                  </template>
                </el-popover>
              </template>
              <SelectPlus
                dictType="ZYML_TABLE_EXTRACT_TYPE"
                v-model="formState.tableExtractType"
                clearable
                style="width: 100%"
                placeholder="请选择抽取方式"
                @change="changeExtractType"
              />
            </el-form-item>
          </el-col>
          <template v-if="formState.tableExtractType === '0'">
            <template v-if="isRelatedDir">
              <template
                v-if="updateCycle && updateCycle !== '' && updateCycle !== null"
              >
                <template v-if="updateCycle === '2' || updateCycle === '3'">
                  <el-col :span="8">
                    <el-form-item label="抽取周期" prop="tableExtractDay">
                      <el-select
                        v-model="formState.tableExtractDay"
                        @change="changeExtractDay"
                        :placeholder="isOnlyRead ? ' ' : '请选择抽取周期'"
                      >
                        <el-option
                          v-for="item in updateCycleENUM[updateCycle]"
                          :label="item.text"
                          :value="item.value"
                          :key="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </template>
                <template v-if="['4', '5', '6', '7'].includes(updateCycle)">
                  <el-col :span="8">
                    <el-form-item label="抽取周期" prop="tableExtractDay">
                      <el-select
                        v-model="formState.tableExtractDay"
                        @change="changeExtractDay"
                        :placeholder="isOnlyRead ? ' ' : '请选择抽取周期'"
                      >
                        <el-option
                          v-for="item in dayUpdateCycleENUM"
                          :label="item.text"
                          :value="item.value"
                          :key="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </template>
              </template>
            </template>
            <template v-else>
              <template v-if="formState.cataId">
                <template v-if="updateCycle === '2' || updateCycle === '3'">
                  <el-col :span="8">
                    <el-form-item label="抽取周期" prop="tableExtractDay">
                      <el-select
                        @change="changeExtractDay"
                        v-model="formState.tableExtractDay"
                        :placeholder="isOnlyRead ? ' ' : '请选择抽取周期'"
                      >
                        <el-option
                          v-for="item in updateCycleENUM[updateCycle]"
                          :label="item.text"
                          :value="item.value"
                          :key="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </template>
                <template v-if="['4', '5', '6', '7'].includes(updateCycle)">
                  <el-col :span="8">
                    <el-form-item label="抽取周期" prop="tableExtractDay">
                      <el-select
                        @change="changeExtractDay"
                        :placeholder="isOnlyRead ? ' ' : '请选择抽取周期'"
                        v-model="formState.tableExtractDay"
                      >
                        <el-option
                          v-for="item in dayUpdateCycleENUM"
                          :label="item.text"
                          :value="item.value"
                          :key="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </template>
              </template>
            </template>
          </template>

          <el-col :span="8">
            <el-form-item label="增量字段" prop="timestampColumnName">
              <el-select
                v-model="formState.timestampColumnName"
                :placeholder="isOnlyRead ? ' ' : '请选择增量字段'"
                style="width: 100%"
              >
                <template v-for="item in formState.dbTableColumnDtoList">
                  <el-option
                    :key="item.fieldCode"
                    v-if="
                      (['0', '3'].includes(formState.tableExtractType) &&
                        item.fieldIsnull === '0') ||
                      (formState.tableExtractType === '5' &&
                        ['6', '7'].includes(item.fieldType))
                    "
                    :value="item.fieldCode"
                    :label="item.fieldCode"
                  ></el-option>
                </template>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序字段" prop="orderField">
              <el-select
                v-model="formState.orderField"
                :placeholder="isOnlyRead ? ' ' : '排序字段'"
                style="width: 100%"
              >
                <el-option
                  v-for="item in formState.dbTableColumnDtoList"
                  :key="item.fieldCode"
                  :value="item.fieldCode"
                  :label="item.fieldCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据条件" prop="tableFilter">
              <template #label>
                数据条件
                <el-popover
                  placement="bottom"
                  width="200"
                  trigger="hover"
                  content="如：columnA = '1' and columnB !='1' or columnC like '%100' )"
                >
                  <template #reference>
                    <i class="el-icon-warning-outline" />
                  </template>
                </el-popover>
              </template>
              <el-input
                v-model.trim="formState.tableFilter"
                maxlength="100"
                type="textarea"
                :placeholder="isOnlyRead ? ' ' : '请输入数据条件'"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8">
            <el-form-item label="创建用户">
              <el-input

                v-model.trim="formState.createUserName"
                maxlength="100"
                placeholder=""
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="创建单位" prop="">
              <el-input

                v-model.trim="formState.createUnitName"
                maxlength="100"
                placeholder=""
                disabled
              />
            </el-form-item>
          </el-col> -->

          <el-col :span="8">
            <el-form-item label="订阅提示" prop="orderContent">
              <el-input
                type="textarea"
                v-model.trim="formState.orderContent"
                maxlength="100"
                :placeholder="isOnlyRead ? '' : '请输入订阅提示'"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="导入订阅附件" prop="orderTipFile">
              <template #label>
                导入订阅附件
                <el-popover
                  placement="bottom"
                  width="200"
                  trigger="hover"
                  content="注: 文件大小不超过50MB,支持的文件格式为jpg，png，gif，txt，doc，docx，pdf，xls，xlsx，ceb，zip，rar。"
                >
                  <template #reference>
                    <i class="el-icon-warning-outline" />
                  </template>
                </el-popover>
              </template>
              <el-button
                v-if="isUploadAttachmentFile"
                :loading="isUploadAttachmentFile"
                type="primary"
                size="mini"
                >上传中</el-button
              >
              <el-upload
                v-else
                action="#"
                accept=""
                :http-request="handleUploadAttachment"
                :before-upload="beforeUploadAttachment"
                :on-success="handleUploadAttachmentSuccess"
                :on-remove="handleRemoveAttachmentDatabase"
                :file-list="formState.orderTipFile"
                :on-preview="handleDownloadOrderTipFile"
              >
                <el-button type="primary" size="mini" :disabled="isOnlyRead"
                  ><i class="el-icon-upload2" style="margin-right: 8px"></i
                  >选择</el-button
                >
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-auth="['switch_res_isCascade']">
            <el-form-item label="是否级联" prop="isCascade">
              <template #label>
                是否级联
                <el-popover
                  placement="bottom"
                  width="200"
                  trigger="hover"
                  content="选是，资源将共享至政务大数据平台"
                >
                  <template #reference>
                    <i class="el-icon-warning-outline" />
                  </template>
                </el-popover>
              </template>
              <el-radio-group
                @input="handleIsCascade"
                v-model="formState.isCascade"
              >
                <el-radio label="1">是</el-radio>
                <el-radio label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </TitleInfo>

    <TitleInfo
      style="width: 100%"
      title="字段属性"
      :animation="true"
      v-model="titleActiveB"
    >
      <template #content>
        <el-row>
          <el-col>
            <el-form-item
              label=""
              label-width="0px"
              prop="dbTableColumnDtoList"
            >
              <div
                v-if="!isOnlyRead"
                style="
                  display: flex;
                  justify-content: flex-end;
                  margin-bottom: 12px;
                "
              >
                <el-button size="mini" @click="handleAddItem">新增</el-button>
              </div>
              <TablePlus
                ref="tableRef"
                :data="formState.dbTableColumnDtoList"
                border
                fit
                height="400"
                stripe
                highlight-current-row
                @header-dragend="handleHeaderDrag"
              >
                <el-table-column
                  prop="fieldCode"
                  label="字段名"
                  width="190"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <el-form-item
                      label=" "
                      label-width="0px"
                      :prop="
                        'dbTableColumnDtoList.' + scope.$index + '.fieldCode'
                      "
                      :rules="rules.paramName"
                    >
                      <el-input
                        v-model.trim="scope.row.fieldCode"
                        style="width: 90%"
                        size="mini"
                        placeholder=" "
                        :maxlength="fieldNameMaxLength"
                        class="input-class-PR47"
                        @focus="handleFieldCodeFocus(scope.row.fieldCode)"
                        @change="handleFieldCodeChange"
                        show-word-limit
                        :disabled="scope.row.flag && scope.row.flag === '1'"
                      />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="fieldName"
                  label="信息项名称"
                  width="210"
                >
                  <template slot-scope="scope">
                    <el-tooltip
                      effect="dark"
                      :content="scope.row.fieldName"
                      :open-delay="500"
                      placement="top-start"
                    >
                      <el-form-item
                        label=" "
                        label-width="0px"
                        :prop="
                          'dbTableColumnDtoList.' + scope.$index + '.fieldName'
                        "
                        :rules="rules.infoName"
                      >
                        <el-input
                          v-if="!props.isFromHangupRes && !isRelatedDir"
                          v-model.trim="scope.row.fieldName"
                          style="width: 90%"
                          size="mini"
                          placeholder="请输入中文"
                          maxlength="100"
                          show-word-limit
                          class="input-class-PR61"
                        />
                        <el-select
                          v-else
                          v-model.trim="scope.row.fieldName"
                          style="width: 90%"
                          size="mini"
                          placeholder=" "
                          filterable
                          allow-create
                          :popper-append-to-body="false"
                          popper-class="fromSystemSelect"
                        >
                          <el-option
                            :value="item.value"
                            :label="item.label"
                            v-for="item in infoItemNameList"
                            :key="item.value"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="fieldType"
                  label="字段类型"
                  width="190"
                  align="center"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <el-form-item
                      label=" "
                      label-width="0px"
                      :prop="
                        'dbTableColumnDtoList.' + scope.$index + '.fieldType'
                      "
                      :rules="rules.paramType"
                    >
                      <SelectPlus
                        dictType="ZYML_FIELD_TYPE"
                        clearable
                        style="width: 90%"
                        v-model="scope.row.fieldType"
                        placeholder=" "
                        :disabled="scope.row.flag && scope.row.flag === '1'"
                      />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="fieldLength"
                  label="字段长度"
                  width="190"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <el-form-item
                      class="custom-label-class"
                      label=" "
                      label-width="0px"
                      :prop="
                        'dbTableColumnDtoList.' + scope.$index + '.fieldLength'
                      "
                      :rules="
                        ![
                          '2',
                          '3',
                          '4',
                          '5',
                          '6',
                          '7',
                          '8',
                          '9',
                          'a',
                          'b'
                        ].includes(scope.row.fieldType)
                          ? rules.paramLength
                          : rules.paramLength2
                      "
                    >
                      <el-input
                        v-model.trim="scope.row.fieldLength"
                        style="width: 90%"
                        size="mini"
                        placeholder=" "
                        :maxlength="10"
                        show-word-limit
                        class="input-class-PR47"
                        :disabled="scope.row.flag && scope.row.flag === '1'"
                      />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="fieldIspk"
                  label="是否主键"
                  width="190"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <el-form-item
                      label=" "
                      label-width="0px"
                      :prop="
                        'dbTableColumnDtoList.' + scope.$index + '.fieldIspk'
                      "
                      :rules="rules.fieldIspk"
                    >
                      <SelectPlus
                        dictType="ZYML_IS_FLAG"
                        clearable
                        style="width: 90%"
                        v-model="scope.row.fieldIspk"
                        placeholder=" "
                        :disabled="
                          scope.row.flag === '1' &&
                          (operationType === 'add'
                            ? initialHasDefaultPrimaryKey
                            : true)
                        "
                        @change="changeFieldIspk(scope.row, scope.$index)"
                      />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="fieldIsnull"
                  label="可否为空"
                  width="190"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <el-form-item
                      label=" "
                      label-width="0px"
                      :rules="rules.fieldIsnull"
                    >
                      <SelectPlus
                        dictType="ZYML_IS_FLAG"
                        clearable
                        style="width: 90%"
                        v-model="scope.row.fieldIsnull"
                        placeholder=" "
                        :disabled="
                          scope.row.fieldIspk === '1' ||
                          (scope.row.flag && scope.row.flag === '1')
                        "
                      />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="methodSketch"
                  label="脱敏规则"
                  width="190"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <el-form-item label=" " label-width="0px">
                      <SelectPlus
                        dictType="ZYML_SENSITIVE_RULE"
                        clearable
                        style="width: 90%"
                        @change="handleSensitiveRuleChange(scope.row, $event)"
                        v-model="scope.row.sensitiveRule"
                        placeholder=" "
                      />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="encryptionRule"
                  label="加密规则"
                  width="190"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <el-form-item
                      label=" "
                      label-width="0px"
                      :rules="rules.encryptionRule"
                    >
                      <SelectPlus
                        dictType="ZYML_ENCRYPTION_RULE"
                        clearable
                        style="width: 90%"
                        v-model="scope.row.encryptionRule"
                        @change="handleEncryptionRuleChange(scope.row, $event)"
                        placeholder=" "
                      />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="sensitiveLevel"
                  label="敏感等级"
                  width="190"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <el-form-item
                      label=" "
                      label-width="0px"
                      :rules="rules.sensitiveLevel"
                    >
                      <SelectPlus
                        dictType="BM_SENSITIVITY_LEVEL"
                        clearable
                        style="width: 90%"
                        v-model="scope.row.sensitiveLevel"
                        placeholder=" "
                      />
                    </el-form-item>
                  </template>
                </el-table-column>

                <!-- <el-table-column
                  label="查询条件"
                  width="190"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <el-form-item
                      label=" "
                      label-width="0px"
                      :prop="
                        'dbTableColumnDtoList.' +
                          scope.$index +
                          '.queryCondition'
                      "
                    >
                      <SelectPlus
                        dictType="ZYML_IS_FLAG"
                        clearable
                        style="width: 90%"
                        v-model="scope.row.queryCondition"
                        placeholder=" "
                        @change="handleClearQueryRelation(scope.row)"
                      />
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="queryRelation"
                  label="查询关系"
                  width="190"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <el-form-item
                      label=" "
                      label-width="0px"
                      :rules="rules.queryRelation"
                    >
                      <SelectPlus
                        dictType="ZYML_QUERY_RELATION"
                        clearable
                        style="width: 90%"
                        :disabled="scope.row.queryCondition !== '1'"
                        v-model="scope.row.queryRelation"
                        placeholder=" "
                      />
                    </el-form-item>
                  </template>
                </el-table-column> -->
                <el-table-column
                  label="操作"
                  width="160"
                  fixed="right"
                  align="center"
                  v-if="!isOnlyRead"
                >
                  <template slot-scope="{ row, $index }">
                    <perm-button-group
                      :config="getDatabaseButtons(row, $index)"
                    />
                  </template>
                </el-table-column>
              </TablePlus>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </TitleInfo>

    <TitleInfo
      style="width: 100%"
      title="权属属性"
      :animation="true"
      v-model="titleActiveC"
    >
      <template #content>
        <el-row type="flex" style="flex-wrap: wrap">
          <el-col :span="8">
            <el-form-item label="联系人" prop="linkman">
              <el-input
                v-model.trim="formState.linkman"
                maxlength="100"
                placeholder="请输入联系人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系部门" prop="contactUnit">
              <el-input
                v-model.trim="formState.contactUnit"
                maxlength="100"
                placeholder="请输入联系部门"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系方式" prop="contactPhone">
              <el-input
                v-model.trim="formState.contactPhone"
                maxlength="100"
                :placeholder="isOnlyRead ? ' ' : '请输入联系方式'"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </TitleInfo>

    <TitleInfo
      style="width: 100%"
      title="共享开放属性"
      :animation="true"
      v-model="titleActiveD"
    >
      <template #content>
        <el-row type="flex" style="flex-wrap: wrap"></el-row>
      </template>
    </TitleInfo>
  </el-form>
</template>

<script setup>
import {
  getCurrentInstance,
  computed,
  reactive,
  ref,
  defineExpose,
  nextTick,
  defineEmits,
  defineProps
} from 'vue'
import useSystemConfig from '../../resourceManage/components/useSystemConfig'
import { FileUtils } from '@/utils/download'
import TablePlus from '@/core/components/TablePlus'
import PermButtonGroup from '@/core/components/PermButtonGroup'
import SelectPlus from '@/core/components/SelectPlus'
import TitleInfo from 'biz/components/common/t-titleInfo'
import { updateCycleENUM, dayUpdateCycleENUM } from 'biz/models/constant.js'
import {
  getTableColsByDsCodeAndTableName,
  queryTableList,
  queryDataSourceList,
  uploadAttachment,
  downloadOrderTipFile,
  checkTableNameCnRepeat,
  countDataSource
} from 'biz/http/api'
import { checkTelehpne } from 'biz/utils/validate'

const emit = defineEmits([
  'openRelateCategoryCallback',
  'importInfoItemCallback'
])

const props = defineProps({
  isFromHangupRes: {
    type: Boolean,
    default: false
  }
})

const { proxy } = getCurrentInstance()

const { isRelatedDir, isDeposit, resOpenTypeSet } = useSystemConfig()
const titleActiveA = ref(true)
const titleActiveB = ref(true)
const titleActiveC = ref(true)
const titleActiveD = ref(true)
const operationType = ref('add')
const isOnlyRead = computed(() =>
  ['view', 'audit', 'hisVersionview', 'hookView'].includes(operationType.value)
)
const formState = reactive({
  tableId: '',
  cataId: '',
  isReflowRes: '0',
  // 新增的三个字段
  assetCategory: '',
  assetCode: '',
  assetType: '',
  dsCode: undefined,
  tableName: '',
  otherTableName: '',
  tableNameCn: '',
  openType: '',
  depositFlag: '0',
  tableExtractType: '',
  tableExtractDay: '',
  timestampColumnName: '',
  orderField: '',
  tableFilter: '',
  linkman: '',
  contactUnit: '',
  contactPhone: '',
  orderContent: '',
  orderTipFileAttachId: '',
  isEmpower: '0',
  cataAuthOrgRelList: [],
  resAuthFileAttachId: '',
  isCascade: '1',
  dbTableColumnDtoList: [],
  resourceId: '',
  tempText: '',
  // createUserName: '',
  // createUnitName: '',
  orderTipFile: [],
  directoryName: '',
  dbSourceType: '',
  tableResType: ''
})

// 是否包含sql中的常用字符
const containDbStr = (value) => {
  value = value.toLowerCase()
  if (
    value.indexOf('=') !== -1 ||
    value.indexOf('!=') !== -1 ||
    value.indexOf('<>') !== -1 ||
    value.indexOf('<=') !== -1 ||
    value.indexOf('>=') !== -1 ||
    value.indexOf('like') !== -1 ||
    (value.indexOf('is') !== -1 &&
      (value.indexOf('null') !== -1 ||
        (value.indexOf('not') !== -1 && value.indexOf('null') !== -1))) ||
    (value.indexOf('between') !== -1 && value.indexOf('and') !== -1) ||
    value.indexOf('exists') !== -1 ||
    value.indexOf('in') !== -1
  ) {
    return true
  } else {
    return false
  }
}

const rules = reactive({
  // 新增字段的验证规则
  assetCategory: [
    { required: true, message: '请选择资产分类', trigger: 'change' }
  ],
  assetCode: [{ required: true, message: '资产编码不能为空', trigger: 'blur' }],
  assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
  paramName: [
    { required: true, message: '请输入字段名', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value) {
          let reg = /[\u4e00-\u9fa5]+/
          if (reg.test(value)) {
            callback(new Error('输入字段禁止输入中文'))
          } else {
            callback()
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  paramType: [{ required: true, message: '请选择类型', trigger: 'change' }],
  paramAttr: [{ required: true, message: '请选择参数属性', trigger: 'change' }],
  paramAddr: [{ required: true, message: '请选择参数位置', trigger: 'change' }],
  fieldIspk: [{ required: true, message: '请选择是否主键', trigger: 'change' }],
  paramLength: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (value) {
          let reg = /^[1-9]\d*$/
          if (reg.test(value)) {
            callback()
          } else {
            callback(new Error('请输入整数'))
          }
        } else {
          callback(new Error('请输入字段长度'))
        }
      },
      trigger: 'change'
    }
  ],
  // 字段类型为5，6,7,8,9的非必填
  paramLength2: [
    { required: false, message: '请输入字段长度', trigger: 'change' }
  ],
  directoryName: [
    { required: true, message: '请选择关联目录', trigger: 'change' }
  ],
  dsCode: [{ required: true, message: '请选择数据源', trigger: 'change' }],
  tableName: [{ required: true, message: '请选择物理表名', trigger: 'change' }],
  otherTableName: [
    { required: true, message: '请输入物理表名', trigger: 'blur' },
    {
      trigger: 'blur',
      validator: (rule, value, callback) => {
        let re = /^(?!_)(?![0-9])(?!.*?_$)[a-zA-Z0-9_]+$/.test(value)
        if (value) {
          if (re) {
            callback()
          } else {
            callback(
              new Error(
                '只允许输入字母、数字及下划线，且不以数字，下划线开头，下划线结尾'
              )
            )
          }
        } else {
          callback()
        }
      }
    }
  ],
  tableNameCn: [
    { required: true, message: '请输入表中文名', trigger: 'blur' },
    {
      trigger: 'blur',
      validator: (rule, value, callback) => {
        let re = /^(?!_)(?!.*?_$)[<>a-zA-Z0-9_\u4e00-\u9fa5\s]+$/.test(value)
        if (value) {
          if (re) {
            checkTableNameCnRepeat({
              tableNameCn: value,
              cataId: formState.cataId,
              resourceId: formState.resourceId
            })
              .then((res) => {
                if (res.code === '200' || res.code === 200) {
                  callback()
                } else {
                  callback(new Error('该表中文名已存在,请重新输入'))
                }
              })
              .catch(() => {
                callback()
              })
          } else {
            callback(
              new Error(
                '只允许输入汉字、字母、数字及下划线，且不以下划线开头和结尾'
              )
            )
          }
        } else {
          callback()
        }
      }
    }
  ],
  openType: [{ required: true, message: '请选择开放类型', trigger: 'change' }],
  tableExtractType: [
    { required: true, message: '请选择抽取方式', trigger: 'change' }
  ],
  tableExtractDay: [
    { required: true, message: '请选择抽取周期', trigger: 'change' }
  ],
  timestampColumnName: [],
  tableFilter: [
    {
      validator: function (rule, value, callback) {
        if (value) {
          // 非纯中文 非纯英文
          let zwMatchs = /^[A-z]+$|^[\u4E00-\u9FA5]+$/
          // 非纯数字
          let szMatchs = /^[0-9]*$/
          if (!zwMatchs.test(value) && !szMatchs.test(value)) {
            // 至少含有 =  !=  <>  <=  >= like not is null  (between and)  exists in
            if (containDbStr(value)) {
              callback()
            } else {
              callback(new Error('请输入正确的数据条件'))
            }
          } else {
            callback(new Error('请输入正确的数据条件'))
          }
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  linkman: [
    { required: true, message: '请输入联系人', trigger: 'blur' },
    {
      trigger: 'blur',
      validator: function (rule, value, callback) {
        let reg = /^[\u4e00-\u9fa5a-zA-Z]+$/
        if (reg.test(value)) {
          callback()
        } else {
          callback(new Error('只能输入中文或英文!'))
        }
      }
    }
  ],
  contactUnit: [
    { required: true, message: '请输入联系部门', trigger: 'blur' },
    {
      trigger: 'blur',
      validator: function (rule, value, callback) {
        let reg = /^[\u4E00-\u9FA5A-Za-z0-9]+$/
        if (reg.test(value)) {
          callback()
        } else {
          callback(new Error('只能输入中文或英文数字!'))
        }
      }
    }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { required: true, validator: checkTelehpne, trigger: 'blur' }
  ]
})

// 处理资产分类变化
const assetCategoryList = ref([])
const handleAssetCategoryChange = async (value) => {
  if (value) {
    // 根据选中的资产分类获取对应的编码
    const selectedCategory = assetCategoryList.value.find(
      (item) => item.key === value
    )
    if (selectedCategory) {
      formState.assetCode = selectedCategory.key // 使用分类的key作为编码
    }
  } else {
    formState.assetCode = ''
  }
}

// 获取资产分类列表
const getAssetCategoryList = async () => {
  try {
    const { code, data } = await proxy.$api.dict.getDictByType({
      type: 'BM_ASSET_CATEGORY'
    })
    if (code === '200' || code === 200) {
      assetCategoryList.value = Object.entries(data).map(([key, value]) => ({
        key,
        value
      }))
    }
  } catch (error) {
    console.error('获取资产分类列表失败:', error)
  }
}

// 清空关联目录信息
const clearDirectory = () => {
  formState.directoryName = ''
  formState.cataId = undefined
}

// 打开关联目录弹窗
const handleOpen = () => {
  emit('openRelateCategoryCallback', formState.isReflowRes)
}

// 改变是否上链
const handleChangeDepositFlag = (val) => {
  if (isDeposit.value && formState.depositFlag === '0') {
    proxy.$message.info('如选择不上链，则后续无法溯源存证！')
  }
}

// 获取可选数据源列表
const dataSourceList = ref([])
const handleQueryDataSource = async () => {
  const { code, data } = await queryDataSourceList({
    tableId: formState.tableId
  })
  if (code === '200' || code === 200) {
    dataSourceList.value = data
  }
}

// 获取可选物理表列表
const dsTableList = ref([])
const changeDs = async () => {
  const { code, data } = await queryTableList({
    dsCode: formState.dsCode
  })
  if (code === '200' || code === 200) {
    dsTableList.value = data
    dsTableList.value.push('其他')

    if (formState.tableName) {
      const isExist = dsTableList.value.includes(formState.tableName)
      let num = 0
      if (isExist) {
        formState.otherTableName = ''
        formState.dbTableColumnDtoList = formState.dbTableColumnDtoList.map(
          (item) => {
            return {
              ...item,
              flag: '1'
            }
          }
        )
        // 检查初始主键状态
        checkInitialPrimaryKey()
      } else {
        formState.otherTableName = formState.tableName
        formState.tableName = '其他'
      }
    }
  }
}

// 变更物理表名
const changeTableName = async (value) => {
  const { code, data } = await getTableColsByDsCodeAndTableName({
    dsCode: formState.dsCode,
    tableName: value
  })
  if (code === '200' || code === 200) {
    formState.dbTableColumnDtoList = data.map((item) => {
      return {
        ...item,
        flag: '1'
      }
    })
    // 检查初始主键状态
    checkInitialPrimaryKey()
  }
}

const tableLengthMap = {
  'rdms-postgresql': 63,
  'rdms-tdsql-oracle': 32,
  'rdms-oracle': 32,
  'rdms-mysql': 32,
  'rdms-iris': 32,
  'rdms-dm': 64,
  'rdms-kingbase-v8r3': 63,
  'rdms-kingbase-v8r6': 63,
  'rdms-tdsql-pg': 63
}

/* eslint-disable */
const tableNameMaxLength = computed(() => {
  const index = dataSourceList.value.findIndex(
    (item) => item.value === formState.dsCode
  )
  if (index !== -1) {
    return tableLengthMap[dataSourceList.value[index]?.dsType] || 100
  } else {
    return 100
  }
})

const fieldLengthMap = {
  'rdms-oracle': 30,
  'rdms-tdsql-oracle': 30,
  'rdms-iris': 30,
  'rdms-mysql': 64,
  'rdms-dm': 64,
  'rdms-postgresql': 63,
  'rdms-kingbase-v8r3': 63,
  'rdms-kingbase-v8r6': 63,
  'rdms-tdsql-pg': 63
}
const fieldNameMaxLength = computed(() => {
  const index = dataSourceList.value.findIndex(
    (item) => item.value === formState.dsCode
  )
  if (index !== -1) {
    return fieldLengthMap[dataSourceList.value[index]?.dsType] || 100
  } else {
    return 30
  }
})

const handleAddItem = (val) => {
  if (val && val !== '' && typeof val === 'string') {
    let reg = /[\u4e00-\u9fa5]+/
    if (reg.test(val)) {
      proxy.$message.info('输入字段禁止输入中文')
      return
    }
    val.split(',').forEach((item) => {
      formState.dbTableColumnDtoList.push({
        fieldCode: item,
        fieldName: '',
        fieldType: '',
        fieldLength: '',
        sensitiveRule: '',
        encryptionRule: '',
        sensitiveLevel: '',
        fieldIspk: '',
        fieldIsnull: '',
        queryCondition: '',
        queryRelation: ''
      })
    })
  } else {
    formState.dbTableColumnDtoList.push({
      fieldCode: '',
      fieldName: '',
      fieldType: '',
      fieldLength: '',
      sensitiveRule: '',
      encryptionRule: '',
      sensitiveLevel: '',
      fieldIspk: '',
      fieldIsnull: '',
      queryCondition: '',
      queryRelation: ''
    })
  }
}

const handleImportInfoItem = (val) => {
  emit('importInfoItemCallback')
}

const tableRef = ref(null)
const handleHeaderDrag = () => {
  nextTick(function () {
    tableRef.value.doLayout()
  })
}

const oldValue = ref('')
const handleFieldCodeChange = (val) => {
  if (formState.timestampColumnName === oldValue.value) {
    formState.timestampColumnName = undefined
  }
  if (formState.orderField === oldValue.value) {
    formState.orderField = undefined
  }
}

const handleFieldCodeFocus = (val) => {
  oldValue.value = val
}

const infoItemNameList = ref([])

const handleSensitiveRuleChange = (row, val) => {
  if (val && val.length) {
    row.encryptionRule = ''
  }
}

const handleEncryptionRuleChange = (row, val) => {
  if (val && val.length) {
    row.sensitiveRule = ''
  }
}

const changeFieldIspk = (value, index) => {
  nextTick(() => {
    if (value.fieldIspk === '1') {
      value.fieldIsnull = '0'
    }
  })
}

const changeExtractType = (value) => {
  nextTick(() => {
    formState.timestampColumnName = undefined
    if (value?.length && value[0] !== '0') {
      formState.tableExtractDay = null
    }
    if (value?.length && value[0] === '5') {
      rules.timestampColumnName = [
        { required: true, message: '请选择增量字段', trigger: 'change' }
      ]
    } else {
      rules.timestampColumnName = []
      formRef.value.clearValidate('timestampColumnName')
    }
  })
}

// 删除字段列表行数据
const handleDeleteTableItem = (row) => {
  proxy
    .$confirm('您确定要删除吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(() => {
      const index = formState.dbTableColumnDtoList.indexOf(row)
      if (formState.orderField === row.fieldCode) {
        formState.orderField = undefined
      }
      if (formState.timestampColumnName === row.fieldCode) {
        formState.timestampColumnName = undefined
      }
      formState.dbTableColumnDtoList.splice(index, 1)
    })
}

const getDatabaseButtons = (row) => {
  let buttons = [
    { label: '删除', icon: 'view', clickFn: handleDeleteTableItem }
  ]

  return {
    row: row,
    buttons,
    showNums: 2
  }
}

// 清空查询关系
const handleClearQueryRelation = (row) => {
  nextTick(() => {
    if (row.queryCondition === '0') {
      row.queryRelation = undefined
    }
  })
}

const updateCycle = ref('')

// 记录初始数据库字段是否有主键的状态
const initialHasDefaultPrimaryKey = ref(false)

// 检查初始数据库字段是否有主键
const checkInitialPrimaryKey = () => {
  initialHasDefaultPrimaryKey.value = formState.dbTableColumnDtoList.some(
    (item) => item.flag === '1' && item.fieldIspk === '1'
  )
}

const isUploadAttachmentFile = ref(false)
// 上传订阅附件
const handleUploadAttachment = (param) => {
  return new Promise(async (resolve, reject) => {
    isUploadAttachmentFile.value = true
    const _file = param.file
    let formData = {}
    formData.multipartFile = _file
    formData.fileName = _file.name

    try {
      const res = await uploadAttachment(formData)
      resolve(res)
      isUploadAttachmentFile.value = false
    } catch (err) {
      reject(err)
      isUploadAttachmentFile.value = false
    }
  })
}

// 上传订阅附件成功回调
const handleUploadAttachmentSuccess = (response) => {
  if (response.code === 200 || response.code === '200') {
    proxy.$message.success(response.message)
    formState.orderTipFile = [
      {
        ...response.data,
        name: response.data.fileName
      }
    ]
    formState.orderTipFileAttachId = response.data.attachId
    formRef.value.clearValidate('orderTipFile')
  } else {
    proxy.$message.info(response.message)
  }
}

// 删除库表订阅附件
const handleRemoveAttachmentDatabase = () => {
  formState.orderTipFile = []
  formState.orderTipFileAttachId = undefined
}

const beforeUploadAttachment = (file) => {
  const fileLength = file.name.split('.').length
  const isMatchFileType = [
    'jpg',
    'png',
    'gif',
    'txt',
    'doc',
    'docx',
    'pdf',
    'xls',
    'xlsx',
    'ceb',
    'zip',
    'rar'
  ].includes(file.name.split('.')[fileLength - 1])
  const isLt50M = file.size / 1024 / 1024 < 50

  if (!isMatchFileType) {
    proxy.$message.info(
      '文件大小不超过50MB,开放资源包含多个文件,建议将文件进行压缩,形成压缩包再上传,支持的文件格式为jpg，png，gif，txt，doc，docx，pdf，xls，xlsx，ceb，zip，rar。'
    )
  }
  if (!isLt50M) {
    proxy.$message.info('文件上传大小不能超过 50MB!')
  }
  return isLt50M && isMatchFileType
}

// 下载订阅提示文件
const handleDownloadOrderTipFile = async (file) => {
  const res = await downloadOrderTipFile({
    attachId: file.attachId,
    resourceId: formState.resourceId
  })
  FileUtils.fileDownload([res], file.name)
}

// 控制是否允许切换是否级联
const canSwitchCascade = ref(true)
const handleIsCascade = () => {
  if (!canSwitchCascade.value) {
    proxy.$message.info('关联的目录不级联，资源不允许级联 ')
    formState.isCascade = '0'
  }
}

const changeExtractDay = () => {
  formRef.value.validateField('tableExtractDay') // 手动校验 抽取周期
}

const formRef = ref(null)
const validateForm = () => {
  return new Promise((resolve, reject) => {
    formRef.value
      .validate()
      .then(() => {
        resolve(true)
      })
      .catch((error) => {
        reject(error)
      })
  })
}

// 目录是否级联
const catalogIsCascade = ref('')
const validateCatalogAndResIsCascade = computed(() => {
  return catalogIsCascade.value === '0' && formState.isCascade === '1'
})

const handleCountDataSource = async () => {
  const { code, data, message } = await countDataSource(formState.dsCode)
  if (code === '200') {
    proxy.$message.success(data)
  } else {
    proxy.$message.info(message)
  }
}

defineExpose({
  formState,
  rules,
  canSwitchCascade,
  updateCycle,
  dataSourceList,
  infoItemNameList,
  assetCategoryList,
  handleQueryDataSource,
  changeDs,
  changeTableName,
  handleAssetCategoryChange,
  validateCatalogAndResIsCascade,
  catalogIsCascade,
  isFromHangupRes: props.isFromHangupRes,
  initData: async (val) => {
    operationType.value = val

    if (isDeposit.value) {
      formState.depositFlag = '1'
    }
    if (val === 'add') {
      handleQueryDataSource()
      // 获取资产分类列表
      getAssetCategoryList()
    }
    // 检查初始主键状态
    checkInitialPrimaryKey()
  },
  validateForm
})
</script>

<style lang="scss" scoped>
.dataServiceSelectPopper {
  width: 200px !important;

  /deep/ .el-select-dropdown__item {
    width: 200px !important;
  }
}

.waysInfo-btnGroup {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 23px;
}

.custom-label-class {
  /deep/ .el-form-item__label {
    width: 12px;
    height: 30px;
  }
}

.input-class-PR61 {
  /deep/ .el-input__inner {
    padding-right: 61px;
  }
}

.input-class-PR47 {
  /deep/ .el-input__inner {
    padding-right: 47px;
  }
}
</style>

<style lang="scss">
.fromSystemSelect {
  width: 200px !important;

  /deep/ .el-select-dropdown__item {
    width: 200px !important;
  }
}
</style>
