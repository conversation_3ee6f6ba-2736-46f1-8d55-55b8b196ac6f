<template>
  <TDrawer
    :visible.sync="visible"
    @close="handleClose"
    :title="props.title"
    v-loading="formLoading"
  >
    <el-form
      ref="formRef"
      :model="formState"
      size="small"
      :rules="rules"
      label-width="140px"
      :disabled="props.isOnlyRead"
      :class="props.isOnlyRead ? 'height100' : ''"
    >
      <el-tabs
        class="smart_city__el-tabs smart_city__el-tabs-flex"
        v-model="activeTab"
        v-if="['historyView', 'view'].includes(type) && props.isShowAuditTab"
      >
        <el-tab-pane label="目录信息" name="info">
          <el-container style="height: 100%">
            <el-scrollbar class="scrollbar-wrapper">
              <TitleInfo
                style="width: 100%"
                title="基本信息"
                :animation="true"
                v-model="titleActiveA"
              >
                <template #content>
                  <el-row type="flex" style="flex-wrap: wrap">
                    <el-col
                      :span="span"
                      v-if="
                        catalogType === 'govData' &&
                        formState.fromPlatform === '1'
                      "
                    >
                      <el-form-item label="来源事项名称" prop="sourceItemName">
                        <el-input
                          clearable
                          v-model="formState.sourceItemName"
                          :maxlength="50"
                          disabled
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="span" v-if="catalogType === 'govData'">
                      <el-form-item label="关联事项名称" prop="relateItemName">
                        <el-input
                          clearable
                          v-model="formState.relateItemName"
                          :maxlength="50"
                          disabled
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="span">
                      <el-form-item label="目录分类" prop="categoryName">
                        <el-input
                          @mouseenter.native="showCategory = true"
                          @mouseleave.native="showCategory = false"
                          v-model.trim="formState.categoryName"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择目录分类'
                          "
                          readonly
                          :disabled="type === 'publish'"
                          @click.native="openDirectory(1)"
                        >
                          <i
                            v-show="
                              showCategory &&
                              formState.categoryName &&
                              !props.isOnlyRead
                            "
                            @click.stop="cleanCategory(1)"
                            slot="suffix"
                            class="el-icon-close"
                          ></i>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item label="关联分类" prop="">
                        <el-input
                          @mouseenter.native="showRelationType = true"
                          @mouseleave.native="showRelationType = false"
                          v-model.trim="formState.relationTypeNames"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择关联分类'
                          "
                          readonly
                          :disabled="type === 'publish'"
                          :maxlength="1000"
                          @click.native="openDirectory(2)"
                        >
                          <i
                            v-show="
                              showRelationType &&
                              formState.relationTypeNames &&
                              !props.isOnlyRead
                            "
                            @click.stop="cleanCategory(2)"
                            slot="suffix"
                            class="el-icon-close"
                          ></i>
                        </el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item
                        label="是否电子证照"
                        prop="certificationType"
                      >
                        <el-radio-group v-model="formState.certificationType">
                          <el-radio label="1">是</el-radio>
                          <el-radio label="0">否</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>

                    <el-col :span="span">
                      <el-form-item label="目录名称" prop="directoryName">
                        <el-input
                          clearable
                          v-model.trim="formState.directoryName"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请输入目录名称'
                          "
                          :maxlength="50"
                          :disabled="type === 'publish'"
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="span">
                      <el-form-item label="目录代码" prop="resourceCode">
                        <el-input
                          v-model.trim="formState.resourceCode"
                          disabled
                          placeholder=" "
                          :maxlength="50"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span" v-auth="['switch_res_isCascade']">
                      <el-form-item label="是否级联" prop="isCascade">
                        <template #label>
                          是否级联
                          <el-popover
                            placement="bottom"
                            width="200"
                            trigger="hover"
                            content="选是，目录将共享至政务大数据平台"
                          >
                            <template #reference>
                              <i class="el-icon-warning-outline" />
                            </template>
                          </el-popover>
                        </template>
                        <el-radio-group v-model="formState.isCascade">
                          <el-radio label="1">是</el-radio>
                          <el-radio label="0">否</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>

                    <el-col :span="span">
                      <el-form-item label="更新周期" prop="updateCycle">
                        <SelectPlus
                          dictType="BM_UPDATE_CYCLE"
                          v-model="formState.updateCycle"
                          clearable
                          :disabled="type === 'publish'"
                          style="width: 100%"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择更新周期'
                          "
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item label="数据提供方式" prop="dataProvideMode">
                        <el-select
                          v-model="formState.dataProvideMode"
                          clearable
                          :multiple="true"
                          style="width: 100%"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择数据提供方式'
                          "
                        >
                          <el-option
                            v-for="item in dataProvideModeDict"
                            :key="item.value"
                            :value="item.value"
                            :label="item.label"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item
                        class="fromSystemStyle"
                        label="信息资源格式"
                        prop="cataFormat"
                      >
                        <el-tooltip
                          v-if="props.isOnlyRead"
                          class="item"
                          effect="dark"
                          :content="formState.cataFormatSecondDesc"
                          :disabled="!formState.cataFormatSecondDesc"
                          placement="top-start"
                        >
                          <el-cascader
                            :props="cascaderProps"
                            v-model="formState.cataFormat"
                            :options="cataFormatList"
                            :placeholder="
                              props.isOnlyRead ? '' : '请选择信息资源格式'
                            "
                            style="width: 100%"
                            collapse-tags
                            :disabled="props.isOnlyRead || type === 'publish'"
                            @change="handleCascaderChange"
                          />
                        </el-tooltip>
                      </el-form-item>
                    </el-col>

                    <el-col :span="span">
                      <el-form-item label="是否涉密" prop="isClassified">
                        <el-radio-group
                          v-model.trim="formState.isClassified"
                          :disabled="type === 'publish'"
                        >
                          <el-radio label="1">是</el-radio>
                          <el-radio label="0">否</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item label="目录提供方" prop="providerDeptName">
                        <el-input
                          v-model.trim="formState.providerDeptName"
                          placeholder=" "
                          disabled
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item label="提供方具体部门">
                        <template #label>
                          提供方具体部门
                          <el-popover
                            placement="bottom"
                            width="200"
                            trigger="hover"
                            content="注: 此处填写提供方具体部门"
                          >
                            <template #reference>
                              <i class="el-icon-warning-outline" />
                            </template>
                          </el-popover>
                        </template>
                        <el-input
                          v-model.trim="formState.internalDep"
                          :maxlength="15"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请输入提供方具体部门'
                          "
                          clearable
                          :disabled="type === 'publish'"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item label="标签" prop="labelName">
                        <el-input
                          v-model.trim="formState.labelName"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '多个标签用英文逗号间隔'
                          "
                          :disabled="type === 'publish'"
                          :maxlength="50"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="16">
                      <el-form-item label="目录摘要" prop="cataAbstract">
                        <el-input
                          type="textarea"
                          style="width: 100%"
                          v-model.trim="formState.cataAbstract"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请输入目录摘要'
                          "
                          clearable
                          :disabled="type === 'publish'"
                          :maxlength="150"
                          show-word-limit
                          :autosize="{ minRows: 3, maxRows: 3 }"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span" v-auth="['dir_empower_use']">
                      <el-form-item label="授权单位">
                        <template #label>
                          授权单位
                          <el-popover
                            placement="bottom"
                            width="300"
                            trigger="hover"
                            content="指定单位查看此目录，未填写所有单位均可查看此目录。"
                          >
                            <template #reference>
                              <i class="el-icon-warning-outline" />
                            </template>
                          </el-popover>
                        </template>
                        <template v-if="props.isOnlyRead">
                          <el-tooltip
                            v-if="formState.authorizeUnit"
                            class="item"
                            effect="dark"
                            :content="formState.authorizeUnit"
                            placement="top-start"
                          >
                            <el-input
                              v-model="formState.authorizeUnit"
                              :placeholder="
                                props.isOnlyRead ? '' : '请输入授权单位'
                              "
                              :disabled="props.isOnlyRead"
                              readonly
                              @click.stop="handleOpenAuthorizeUnitDrawer"
                            />
                          </el-tooltip>
                          <el-input
                            v-else
                            v-model="formState.authorizeUnit"
                            :placeholder="
                              props.isOnlyRead ? '' : '请输入授权单位'
                            "
                            :disabled="props.isOnlyRead"
                            readonly
                            @click.stop="handleOpenAuthorizeUnitDrawer"
                          />
                        </template>

                        <el-input
                          v-else
                          v-model="formState.authorizeUnit"
                          :placeholder="
                            props.isOnlyRead ? '' : '请输入授权单位'
                          "
                          :disabled="props.isOnlyRead"
                          readonly
                          @click.native="handleOpenAuthorizeUnitDrawer"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item label="数据区域范围" prop="areaRange">
                        <SelectPlus
                          dictType="BM_AREA_RANGE"
                          v-model="formState.areaRange"
                          clearable
                          style="width: 100%"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择数据区域范围'
                          "
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item label="数据时间范围" prop="date">
                        <el-date-picker
                          v-model="formState.date"
                          type="daterange"
                          align="right"
                          unlink-panels
                          style="width: 100%"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          :picker-options="pickerOptions"
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="span">
                      <el-form-item
                        label="是否存在来源系统"
                        prop="isHavingSystem"
                      >
                        <el-radio-group
                          v-model="formState.isHavingSystem"
                          @change="changeSystem"
                          :disabled="type === 'publish'"
                        >
                          <el-radio label="1">是</el-radio>
                          <el-radio label="0">否</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>

                    <el-col
                      :span="span"
                      v-if="
                        formState.isHavingSystem === '1' && props.isOnlyRead
                      "
                    >
                      <el-form-item
                        class="fromSystemStyle"
                        label="来源系统"
                        prop="fromSystem"
                      >
                        <el-input v-model="formState.systemName" disabled />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item label="提供渠道" prop="netType">
                        <SelectPlus
                          dictType="BM_NET_TYPE"
                          v-model.trim="formState.netType"
                          clearable
                          style="width: 100%"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择提供渠道'
                          "
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item label="应用场景">
                        <SelectPlus
                          dictType="BM_USE_TYPE"
                          v-model="formState.useType"
                          clearable
                          style="width: 100%"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择应用场景'
                          "
                        />
                      </el-form-item>
                    </el-col>

                    <el-col :span="span">
                      <el-form-item label="所属领域">
                        <SelectPlus
                          dictType="BM_DOMAIN"
                          v-model="formState.domian"
                          clearable
                          style="width: 100%"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择所属领域'
                          "
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item label="数据层级" prop="dataLevel">
                        <SelectPlus
                          dictType="DATA_LEVEL"
                          v-model="formState.dataLevel"
                          clearable
                          style="width: 100%"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择数据层级'
                          "
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>
              </TitleInfo>

              <TitleInfo
                style="width: 100%"
                title="共享开放属性"
                :animation="true"
                v-model="titleActiveB"
              >
                <template #content>
                  <el-row>
                    <el-col :span="span">
                      <el-form-item label="共享类型" prop="shardType">
                        <SelectPlus
                          dictType="BM_SHARD_TYPE"
                          v-model="formState.shardType"
                          clearable
                          style="width: 100%"
                          :disabled="
                            type === 'maintenance' || type === 'publish'
                          "
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择共享类型'
                          "
                          @change="changeShardType"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item label="共享方式" prop="sharindMethod">
                        <SelectPlus
                          dictType="BM_SHARIND_METHOD"
                          v-model="formState.sharindMethod"
                          style="width: 100%"
                          clearable
                          :disabled="
                            type === 'maintenance' || type === 'publish'
                          "
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择共享方式'
                          "
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item
                        label="共享要求"
                        prop="sharindConditionArr"
                        v-if="formState.shardType === '2'"
                      >
                        <template
                          #label
                          v-if="formState.shardType && !props.isOnlyRead"
                        >
                          共享要求
                          <el-popover
                            placement="bottom"
                            width="60"
                            trigger="hover"
                            :content="sharindConditionTitle"
                          >
                            <template #reference>
                              <i class="el-icon-warning-outline" />
                            </template>
                          </el-popover>
                        </template>
                        <el-tooltip
                          v-if="
                            formState.sharindConditionArr &&
                            formState.sharindConditionArr.length
                          "
                          class="item"
                          effect="dark"
                          :content="formState.sharindCondition"
                          placement="top-start"
                        >
                          <el-select
                            multiple
                            style="width: 100%"
                            v-model="formState.sharindConditionArr"
                            clearable
                            collapse-tags
                            :disabled="type === 'publish'"
                            :placeholder="
                              props.isOnlyRead ? ' ' : '请选择共享要求'
                            "
                          >
                            <el-option
                              v-for="item in shardType"
                              :label="item.label"
                              :value="item.value"
                              :key="item.value"
                            >
                            </el-option>
                          </el-select>
                        </el-tooltip>
                        <el-select
                          v-else
                          multiple
                          style="width: 100%"
                          v-model="formState.sharindConditionArr"
                          clearable
                          collapse-tags
                          :disabled="type === 'publish'"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择共享要求'
                          "
                        >
                          <el-option
                            v-for="item in shardType"
                            :label="item.label"
                            :value="item.value"
                            :key="item.value"
                          >
                          </el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item
                        label="共享要求"
                        prop="sharindCondition"
                        v-else
                      >
                        <template
                          #label
                          v-if="formState.shardType && !props.isOnlyRead"
                        >
                          共享要求
                          <el-popover
                            placement="bottom"
                            width="60"
                            trigger="hover"
                            :content="sharindConditionTitle"
                          >
                            <template #reference>
                              <i class="el-icon-warning-outline" />
                            </template>
                          </el-popover>
                        </template>

                        <el-input
                          v-model.trim="formState.sharindCondition"
                          :placeholder="sharindConditionTitle"
                          :maxlength="60"
                          clearable
                          :disabled="isOnlyRead || type === 'publish'"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="span">
                      <el-form-item label="开放类型" prop="openType">
                        <SelectPlus
                          dictType="BM_OPEN_TYPE"
                          v-model="formState.openType"
                          clearable
                          style="width: 100%"
                          :disabled="
                            type === 'maintenance' || type === 'publish'
                          "
                          @change="changeOpenType"
                          :placeholder="
                            props.isOnlyRead ? ' ' : '请选择开放类型'
                          "
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <el-form-item label="开放方式" prop="openApproach">
                        <SelectPlus
                          dictType="BM_SHARIND_METHOD"
                          v-model="formState.openApproach"
                          clearable
                          style="width: 100%"
                          :disabled="
                            type === 'maintenance' || type === 'publish'
                          "
                          :placeholder="
                            props.isOnlyRead ? '' : '请选择开放方式'
                          "
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="span">
                      <!-- v-if="formState.openType === '1'" -->
                      <el-form-item label="开放要求" prop="openCondition">
                        <el-input
                          v-model.trim="formState.openCondition"
                          :placeholder="
                            props.isOnlyRead ? '' : '说明开放要求和开放范围'
                          "
                          :maxlength="100"
                          clearable
                          :disabled="type === 'publish'"
                        ></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>
              </TitleInfo>
              <TitleInfo
                style="width: 100%"
                title="信息项"
                :animation="true"
                v-model="titleActiveC"
              >
                <template #content>
                  <!--列表表格区-->
                  <div
                    v-if="!props.isOnlyRead && type !== 'publish'"
                    style="text-align: right; margin-bottom: 10px"
                  >
                    <perm-button
                      label="新增"
                      type="primary"
                      icon="add"
                      size="mini"
                      style="margin-left: 12px"
                      @click="handleAdd"
                    />
                  </div>
                  <el-form-item label-width="0px">
                    <TablePlus
                      :data="list"
                      ref="ItemsTable"
                      border
                      fit
                      stripe
                      height="300px"
                      highlight-current-row
                      v-loading="listLoading"
                      @selection-change="selectMainTableRow"
                      @header-dragend="handleHeaderDrag"
                    >
                      <el-table-column
                        v-if="!props.isOnlyRead"
                        type="selection"
                        width="60"
                        align="center"
                      >
                      </el-table-column>
                      <!-- <el-table-column
                        label="序号"
                        type="index"
                        width="60"
                        align="center"
                      >
                      </el-table-column> -->
                      <el-table-column
                        prop="itemName"
                        label="信息项名称"
                        min-width="160"
                        show-overflow-tooltip
                      >
                      </el-table-column>
                      <el-table-column
                        prop="dataTypeCode"
                        label="数据类型"
                        min-width="130"
                        align="center"
                        show-overflow-tooltip
                      >
                      </el-table-column>
                      <el-table-column
                        prop="timeTypeCode"
                        label="时间类型"
                        min-width="130"
                        align="center"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="accuracy"
                        label="精度"
                        min-width="130"
                        align="center"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="dataLength"
                        label="数据长度"
                        min-width="130"
                        align="right"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        prop="isPublicCode"
                        label="是否向社会开放"
                        width="130"
                        align="center"
                        show-overflow-tooltip
                      >
                      </el-table-column>
                      <el-table-column
                        prop="sensitivityLevelCode"
                        label="数据敏感级别"
                        width="130"
                        align="center"
                        show-overflow-tooltip
                      >
                      </el-table-column>
                      <el-table-column
                        prop="itemDesc"
                        label="信息项描述"
                        min-width="180"
                        show-overflow-tooltip
                      >
                      </el-table-column>
                      <el-table-column
                        prop="createdTime"
                        label="创建时间"
                        align="center"
                        width="220"
                        show-overflow-tooltip
                      >
                      </el-table-column>
                      <el-table-column
                        prop="lastUpdatedTime"
                        label="修改时间"
                        align="center"
                        width="220"
                        show-overflow-tooltip
                      >
                      </el-table-column>
                      <el-table-column
                        v-if="!props.isOnlyRead"
                        label="操作"
                        align="center"
                        fixed="right"
                        header-align="center"
                        min-width="200"
                      >
                        <template slot-scope="scope">
                          <perm-button-group
                            :config="getInfoItemButtons(scope)"
                          />
                        </template>
                      </el-table-column>
                    </TablePlus>
                  </el-form-item>
                </template>
              </TitleInfo>
            </el-scrollbar>
          </el-container>
        </el-tab-pane>
        <el-tab-pane label="审批进度" name="process">
          <el-container style="height: 100%">
            <el-scrollbar class="scrollbar-wrapper">
              <ProgressView
                v-if="activeTab === 'process'"
                ref="progressRef"
                :auditRecord="auditRecord"
              />
            </el-scrollbar>
          </el-container>
        </el-tab-pane>
      </el-tabs>
      <template v-else>
        <TitleInfo
          style="width: 100%"
          title="基本信息"
          :animation="true"
          v-model="titleActiveA"
        >
          <template #content>
            <el-row type="flex" style="flex-wrap: wrap">
              <el-col
                :span="span"
                v-if="
                  catalogType === 'govData' && formState.fromPlatform === '1'
                "
              >
                <el-form-item label="来源事项名称" prop="sourceItemName">
                  <el-input
                    clearable
                    v-model="formState.sourceItemName"
                    :maxlength="50"
                    disabled
                  />
                </el-form-item>
              </el-col>

              <el-col :span="span" v-if="catalogType === 'govData'">
                <el-form-item label="关联事项名称" prop="relateItemName">
                  <el-input
                    clearable
                    v-model="formState.relateItemName"
                    :maxlength="50"
                    disabled
                  />
                </el-form-item>
              </el-col>

              <el-col :span="span">
                <el-form-item label="目录分类" prop="categoryName">
                  <el-input
                    @mouseenter.native="showCategory = true"
                    @mouseleave.native="showCategory = false"
                    v-model.trim="formState.categoryName"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择目录分类'"
                    readonly
                    :disabled="type === 'publish'"
                    @click.native="openDirectory(1)"
                  >
                    <i
                      v-show="
                        showCategory &&
                        formState.categoryName &&
                        !props.isOnlyRead
                      "
                      @click.stop="cleanCategory(1)"
                      slot="suffix"
                      class="el-icon-close"
                    ></i>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="关联分类" prop="">
                  <el-input
                    @mouseenter.native="showRelationType = true"
                    @mouseleave.native="showRelationType = false"
                    v-model.trim="formState.relationTypeNames"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择关联分类'"
                    readonly
                    :disabled="type === 'publish'"
                    :maxlength="1000"
                    @click.native="openDirectory(2)"
                  >
                    <i
                      v-show="
                        showRelationType &&
                        formState.relationTypeNames &&
                        !props.isOnlyRead
                      "
                      @click.stop="cleanCategory(2)"
                      slot="suffix"
                      class="el-icon-close"
                    ></i>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="是否电子证照" prop="certificationType">
                  <el-radio-group v-model="formState.certificationType">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col :span="span">
                <el-form-item label="目录名称" prop="directoryName">
                  <el-input
                    clearable
                    v-model.trim="formState.directoryName"
                    :placeholder="props.isOnlyRead ? ' ' : '请输入目录名称'"
                    :maxlength="50"
                    :disabled="type === 'publish'"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="span">
                <el-form-item label="目录代码" prop="resourceCode">
                  <el-input
                    v-model.trim="formState.resourceCode"
                    disabled
                    placeholder=" "
                    :maxlength="50"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="span" v-auth="['switch_res_isCascade']">
                <el-form-item label="是否级联" prop="isCascade">
                  <el-radio-group v-model="formState.isCascade">
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>

              <el-col :span="span">
                <el-form-item label="更新周期" prop="updateCycle">
                  <SelectPlus
                    dictType="BM_UPDATE_CYCLE"
                    v-model="formState.updateCycle"
                    clearable
                    :disabled="type === 'publish'"
                    style="width: 100%"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择更新周期'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="数据提供方式" prop="dataProvideMode">
                  <el-select
                    v-model="formState.dataProvideMode"
                    clearable
                    :multiple="true"
                    style="width: 100%"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择数据提供方式'"
                  >
                    <el-option
                      v-for="item in dataProvideModeDict"
                      :key="item.value"
                      :value="item.value"
                      :label="item.label"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item
                  class="fromSystemStyle"
                  label="信息资源格式"
                  prop="cataFormat"
                >
                  <el-cascader
                    :props="cascaderProps"
                    v-model="formState.cataFormat"
                    :options="cataFormatList"
                    :placeholder="props.isOnlyRead ? '' : '请选择信息资源格式'"
                    style="width: 100%"
                    collapse-tags
                    :disabled="type === 'publish'"
                    @change="handleCascaderChange"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="span">
                <el-form-item label="是否涉密" prop="isClassified">
                  <el-radio-group
                    v-model.trim="formState.isClassified"
                    :disabled="type === 'publish'"
                  >
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="目录提供方" prop="providerDeptName">
                  <el-input
                    v-model.trim="formState.providerDeptName"
                    placeholder=" "
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="提供方具体部门">
                  <template #label>
                    提供方具体部门
                    <el-popover
                      placement="bottom"
                      width="200"
                      trigger="hover"
                      content="注: 此处填写提供方具体部门"
                    >
                      <template #reference>
                        <i class="el-icon-warning-outline" />
                      </template>
                    </el-popover>
                  </template>
                  <el-input
                    v-model.trim="formState.internalDep"
                    :maxlength="15"
                    :placeholder="
                      props.isOnlyRead ? ' ' : '请输入提供方具体部门'
                    "
                    clearable
                    :disabled="type === 'publish'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="标签" prop="labelName">
                  <el-input
                    v-model.trim="formState.labelName"
                    :placeholder="
                      props.isOnlyRead ? ' ' : '多个标签用英文逗号间隔'
                    "
                    :disabled="type === 'publish'"
                    :maxlength="50"
                    clearable
                  />
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="目录摘要" prop="cataAbstract">
                  <el-input
                    type="textarea"
                    style="width: 100%"
                    v-model.trim="formState.cataAbstract"
                    :placeholder="props.isOnlyRead ? ' ' : '请输入目录摘要'"
                    clearable
                    :disabled="type === 'publish'"
                    :maxlength="150"
                    show-word-limit
                    :autosize="{ minRows: 3, maxRows: 3 }"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8" v-auth="['dir_empower_use']">
                <el-form-item label="授权单位">
                  <template #label>
                    授权单位
                    <el-popover
                      placement="bottom"
                      width="300"
                      trigger="hover"
                      content="指定单位查看此目录，未填写所有单位均可查看此目录。"
                    >
                      <template #reference>
                        <i class="el-icon-warning-outline" />
                      </template>
                    </el-popover>
                  </template>
                  <template v-if="props.isOnlyRead">
                    <el-tooltip
                      v-if="formState.authorizeUnit"
                      class="item"
                      effect="dark"
                      :content="formState.authorizeUnit"
                      placement="top-start"
                    >
                      <el-input
                        v-model="formState.authorizeUnit"
                        :placeholder="props.isOnlyRead ? '' : '请输入授权单位'"
                        :disabled="props.isOnlyRead"
                        readonly
                        @click.stop="handleOpenAuthorizeUnitDrawer"
                      />
                    </el-tooltip>
                    <el-input
                      v-else
                      v-model="formState.authorizeUnit"
                      :placeholder="props.isOnlyRead ? '' : '请输入授权单位'"
                      :disabled="props.isOnlyRead"
                      readonly
                      @click.stop="handleOpenAuthorizeUnitDrawer"
                    />
                  </template>

                  <el-input
                    v-else
                    @mouseenter.native="showAuthorizeUnitClearIcon = true"
                    @mouseleave.native="showAuthorizeUnitClearIcon = false"
                    v-model="formState.authorizeUnit"
                    :placeholder="props.isOnlyRead ? '' : '请输入授权单位'"
                    :disabled="props.isOnlyRead"
                    readonly
                    @click.native="handleOpenAuthorizeUnitDrawer"
                  >
                    <i
                      v-show="
                        showAuthorizeUnitClearIcon && formState.authorizeUnit
                      "
                      @click.stop="handleClearAuthorizeUnit"
                      slot="suffix"
                      class="el-icon-close"
                    ></i>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="数据区域范围" prop="areaRange">
                  <SelectPlus
                    dictType="BM_AREA_RANGE"
                    v-model="formState.areaRange"
                    clearable
                    style="width: 100%"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择数据区域范围'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="数据时间范围" prop="date">
                  <el-date-picker
                    v-model="formState.date"
                    type="daterange"
                    value-format="yyyy-MM-dd"
                    align="right"
                    unlink-panels
                    style="width: 100%"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    :picker-options="pickerOptions"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="是否存在来源系统" prop="isHavingSystem">
                  <el-radio-group
                    v-model="formState.isHavingSystem"
                    @change="changeSystem"
                    :disabled="type === 'publish'"
                  >
                    <el-radio label="1">是</el-radio>
                    <el-radio label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col
                :span="span"
                v-if="formState.isHavingSystem === '1' && !props.isOnlyRead"
              >
                <el-form-item
                  class="fromSystemStyle"
                  label="来源系统"
                  prop="fromSystem"
                >
                  <div style="display: flex; gap: 8px">
                    <el-select
                      ref="selectRef"
                      v-model="formState.fromSystem"
                      :placeholder="props.isOnlyRead ? ' ' : '请选择来源系统'"
                      filterable
                      style="width: 100%"
                      clearable
                      :disabled="type === 'publish'"
                      :popper-append-to-body="false"
                      popper-class="fromSystemSelect"
                      :loading="searchLoad"
                      :filter-method="filterMethod"
                      v-loadmore="loadMore(rangeNumber)"
                      @visible-change="visibleChange"
                    >
                      <el-tooltip
                        v-for="item in fromSystemList.slice(0, rangeNumber)"
                        :key="item.systemId"
                        effect="dark"
                        :content="item.systemName"
                        :open-delay="500"
                        placement="top-start"
                      >
                        <el-option
                          :disabled="item.enableFlag !== '1'"
                          :label="item.systemName"
                          :value="item.systemId"
                        >
                        </el-option>
                      </el-tooltip>
                      <!-- <el-pagination
                      :page-size="queryParams.pageSize"
                      :total="total"
                      @size-change="handleSizeChange"
                      @current-change="handleCurrentChange"
                      layout="prev, pager, next"
                    /> -->
                    </el-select>
                    <!-- <el-button type="primary" @click="systemAdd">
                      新增
                    </el-button> -->
                  </div>
                </el-form-item>
              </el-col>
              <el-col
                :span="span"
                v-if="formState.isHavingSystem === '1' && props.isOnlyRead"
              >
                <el-form-item
                  class="fromSystemStyle"
                  label="来源系统"
                  prop="fromSystem"
                >
                  <el-input v-model="formState.systemName" disabled />
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="提供渠道" prop="netType">
                  <SelectPlus
                    dictType="BM_NET_TYPE"
                    v-model.trim="formState.netType"
                    clearable
                    style="width: 100%"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择提供渠道'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="应用场景">
                  <SelectPlus
                    dictType="BM_USE_TYPE"
                    v-model="formState.useType"
                    clearable
                    style="width: 100%"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择应用场景'"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="span">
                <el-form-item label="所属领域">
                  <SelectPlus
                    dictType="BM_DOMAIN"
                    v-model="formState.domian"
                    clearable
                    style="width: 100%"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择所属领域'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="数据层级" prop="dataLevel">
                  <SelectPlus
                    dictType="DATA_LEVEL"
                    v-model="formState.dataLevel"
                    clearable
                    style="width: 100%"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择数据层级'"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </TitleInfo>

        <TitleInfo
          style="width: 100%"
          title="共享开放属性"
          :animation="true"
          v-model="titleActiveB"
        >
          <template #content>
            <el-row>
              <el-col :span="span">
                <el-form-item label="共享类型" prop="shardType">
                  <SelectPlus
                    dictType="BM_SHARD_TYPE"
                    v-model="formState.shardType"
                    clearable
                    style="width: 100%"
                    :disabled="type === 'maintenance' || type === 'publish'"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择共享类型'"
                    @change="changeShardType"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="共享方式" prop="sharindMethod">
                  <SelectPlus
                    dictType="BM_SHARIND_METHOD"
                    v-model="formState.sharindMethod"
                    style="width: 100%"
                    clearable
                    :disabled="type === 'maintenance' || type === 'publish'"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择共享方式'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item
                  label="共享要求"
                  prop="sharindConditionArr"
                  v-if="formState.shardType === '2'"
                >
                  <template
                    #label
                    v-if="formState.shardType && !props.isOnlyRead"
                  >
                    共享要求
                    <el-popover
                      placement="bottom"
                      width="60"
                      trigger="hover"
                      :content="sharindConditionTitle"
                    >
                      <template #reference>
                        <i class="el-icon-warning-outline" />
                      </template>
                    </el-popover>
                  </template>
                  <el-tooltip
                    v-if="
                      formState.sharindConditionArr &&
                      formState.sharindConditionArr.length
                    "
                    class="item"
                    effect="dark"
                    :content="formState.sharindCondition"
                    placement="top-start"
                  >
                    <el-select
                      multiple
                      style="width: 100%"
                      v-model="formState.sharindConditionArr"
                      clearable
                      collapse-tags
                      :disabled="type === 'publish'"
                      :placeholder="props.isOnlyRead ? ' ' : '请选择共享要求'"
                    >
                      <el-option
                        v-for="item in shardType"
                        :label="item.label"
                        :value="item.value"
                        :key="item.value"
                      >
                      </el-option>
                    </el-select>
                  </el-tooltip>
                  <el-select
                    v-else
                    multiple
                    style="width: 100%"
                    v-model="formState.sharindConditionArr"
                    clearable
                    collapse-tags
                    :disabled="type === 'publish'"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择共享要求'"
                  >
                    <el-option
                      v-for="item in shardType"
                      :label="item.label"
                      :value="item.value"
                      :key="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="共享要求" prop="sharindCondition" v-else>
                  <template
                    #label
                    v-if="formState.shardType && !props.isOnlyRead"
                  >
                    共享要求
                    <el-popover
                      placement="bottom"
                      width="60"
                      trigger="hover"
                      :content="sharindConditionTitle"
                    >
                      <template #reference>
                        <i class="el-icon-warning-outline" />
                      </template>
                    </el-popover>
                  </template>

                  <el-input
                    v-model.trim="formState.sharindCondition"
                    :placeholder="sharindConditionTitle"
                    :maxlength="60"
                    clearable
                    :disabled="isOnlyRead || type === 'publish'"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="span">
                <el-form-item label="开放类型" prop="openType">
                  <SelectPlus
                    dictType="BM_OPEN_TYPE"
                    v-model="formState.openType"
                    clearable
                    style="width: 100%"
                    :disabled="type === 'maintenance' || type === 'publish'"
                    @change="changeOpenType"
                    :placeholder="props.isOnlyRead ? ' ' : '请选择开放类型'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <el-form-item label="开放方式" prop="openApproach">
                  <SelectPlus
                    dictType="BM_SHARIND_METHOD"
                    v-model="formState.openApproach"
                    clearable
                    style="width: 100%"
                    :disabled="type === 'maintenance' || type === 'publish'"
                    :placeholder="props.isOnlyRead ? '' : '请选择开放方式'"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="span">
                <!-- v-if="formState.openType === '1'" -->
                <el-form-item label="开放要求" prop="openCondition">
                  <el-input
                    v-model.trim="formState.openCondition"
                    :placeholder="
                      props.isOnlyRead ? '' : '说明开放要求和开放范围'
                    "
                    :maxlength="100"
                    clearable
                    :disabled="type === 'publish'"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </template>
        </TitleInfo>
        <TitleInfo
          style="width: 100%"
          title="信息项"
          :animation="true"
          v-model="titleActiveC"
        >
          <template #content>
            <!--列表表格区-->
            <div
              v-if="!props.isOnlyRead && type !== 'publish'"
              style="text-align: right; margin-bottom: 10px"
            >
              <perm-button
                label="新增"
                type="primary"
                icon="add"
                size="mini"
                style="margin-left: 12px"
                @click="handleAdd"
              />
            </div>
            <el-form-item label-width="0px">
              <TablePlus
                :data="list"
                ref="ItemsTable"
                border
                fit
                stripe
                height="300px"
                highlight-current-row
                v-loading="listLoading"
                @selection-change="selectMainTableRow"
                @header-dragend="handleHeaderDrag"
              >
                <el-table-column
                  v-if="!props.isOnlyRead"
                  type="selection"
                  width="60"
                  align="center"
                >
                </el-table-column>
                <!-- <el-table-column
                  label="序号"
                  type="index"
                  width="60"
                  align="center"
                >
                </el-table-column> -->
                <el-table-column
                  prop="itemName"
                  label="信息项名称"
                  min-width="160"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="dataTypeCode"
                  label="数据类型"
                  min-width="130"
                  align="center"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="timeTypeCode"
                  label="时间类型"
                  min-width="130"
                  align="center"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="accuracy"
                  label="精度"
                  min-width="130"
                  align="center"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="dataLength"
                  label="数据长度"
                  min-width="130"
                  align="right"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="isPublicCode"
                  label="是否向社会开放"
                  width="130"
                  align="center"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="sensitivityLevelCode"
                  label="数据敏感级别"
                  width="130"
                  align="center"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="itemDesc"
                  label="信息项描述"
                  min-width="180"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="createdTime"
                  label="创建时间"
                  align="center"
                  width="220"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  prop="lastUpdatedTime"
                  label="修改时间"
                  align="center"
                  width="220"
                  show-overflow-tooltip
                >
                </el-table-column>
                <el-table-column
                  v-if="!props.isOnlyRead"
                  label="操作"
                  align="center"
                  fixed="right"
                  header-align="center"
                  min-width="200"
                >
                  <template slot-scope="scope">
                    <perm-button-group :config="getInfoItemButtons(scope)" />
                  </template>
                </el-table-column>
              </TablePlus>
            </el-form-item>
          </template>
        </TitleInfo>
      </template>
    </el-form>

    <template #footerForm>
      <TitleInfo
        v-if="type === 'maintenance'"
        style="width: 100%"
        title="变更信息"
        :animation="true"
        v-model="titleActiveD"
      >
        <template #content>
          <el-form
            ref="changeFormRef"
            :model="changeFormState"
            label-width="140px"
            size="small"
            :rules="changeFormRules"
          >
            <el-row type="flex" style="flex-wrap: wrap">
              <el-col>
                <el-form-item label="变更说明" prop="maintainReason">
                  <div style="display: flex; flex-direction: column">
                    <div style="margin-bottom: 6px">
                      <el-tag
                        style="cursor: pointer"
                        @click="handleSetMaintainReason('文件要求')"
                        >文件要求</el-tag
                      >
                      <el-tag
                        style="cursor: pointer"
                        @click="handleSetMaintainReason('机构调整')"
                        >机构调整</el-tag
                      >
                      <el-tag
                        style="cursor: pointer"
                        @click="handleSetMaintainReason('供需对接')"
                        >供需对接</el-tag
                      >
                      <el-tag
                        style="cursor: pointer"
                        @click="handleSetMaintainReason('异议')"
                        >异议</el-tag
                      >
                      <el-tag
                        style="cursor: pointer"
                        @click="handleSetMaintainReason('系统调整')"
                        >系统调整</el-tag
                      >
                    </div>
                    <el-input
                      type="textarea"
                      style="width: 100%"
                      v-model.trim="changeFormState.maintainReason"
                      placeholder="请输入变更说明"
                      clearable
                      :maxlength="150"
                      show-word-limit
                      :autosize="{ minRows: 3, maxRows: 3 }"
                    />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-form
            v-if="props.type === 'goback'"
            ref="revokedFormRef"
            :model="revokedFormState"
            label-width="140px"
            size="small"
            :rules="revokedFormRules"
          >
            <el-row type="flex" style="flex-wrap: wrap">
              <el-col :span="12">
                <el-form-item label="撤销说明" prop="cancelReason">
                  <el-input
                    type="textarea"
                    style="width: 100%"
                    v-model.trim="formState.cancelReason"
                    placeholder="请输入撤销说明"
                    clearable
                    :maxlength="150"
                    show-word-limit
                    :autosize="{ minRows: 3, maxRows: 3 }"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="撤销理由" prop="cancelType">
                  <el-radio-group v-model="formState.cancelType">
                    <el-radio label="0">暂停发布</el-radio>
                    <el-radio label="1">撤销目录</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </template>
      </TitleInfo>
    </template>

    <template #footer>
      <el-button
        v-if="!['historyView', 'view'].includes(type)"
        size="mini"
        type="primary"
        class="close-btn"
        @click="handleClose"
        :loading="btnLoading"
        >取消</el-button
      >
      <el-button
        v-else
        size="mini"
        type="primary"
        class="close-btn"
        @click="handleClose"
        :loading="btnLoading"
        >关闭</el-button
      >
      <el-button
        v-if="type === 'add' || type === 'edit' || type === 'quote'"
        size="mini"
        type="primary"
        @click="save(1)"
        :loading="btnLoading"
        >保存</el-button
      >
      <el-button
        v-if="type === 'add' || type === 'edit' || type === 'quote'"
        size="mini"
        @click="save(2)"
        :loading="btnLoading"
        >保存并送审</el-button
      >
      <el-button
        v-if="type === 'maintenance'"
        size="mini"
        type="primary"
        @click="maintenance"
        :loading="btnLoading"
      >
        变更
      </el-button>
    </template>
  </TDrawer>
</template>

<script>
import {
  defineComponent,
  computed,
  getCurrentInstance,
  nextTick,
  toRefs,
  reactive,
  watch,
  ref
} from 'vue'
import SelectPlus from '@/core/components/SelectPlus'
import TablePlus from '@/core/components/TablePlus'
import PermButton from '@/core/components/PermButton'
import PermButtonGroup from '@/core/components/PermButtonGroup'
import TDrawer from 'biz/components/common/t-drawer'
import TitleInfo from 'biz/components/common/t-titleInfo'
import { formatWithSeperator } from '@/biz/utils/datetime'
import ProgressView from 'biz/components/business/progressView'
import { throttle } from 'lodash-es'
import { getInfoItemByEntityId } from 'biz/http/api'

export default defineComponent({
  name: 'AddCategoryDrawer',
  components: {
    TDrawer,
    SelectPlus,
    TitleInfo,
    TablePlus,
    PermButton,
    PermButtonGroup,
    ProgressView
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '授权界面'
    },
    isOnlyRead: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'add'
    },
    catalogType: {
      type: String,
      default: 'govInfo'
    },
    isShowAuditTab: {
      type: Boolean,
      default: true
    }
  },
  emits: [
    'authorizeCallback',
    'authorizeSuccessCallback',
    'handleDetailCallback',
    'openRelateClassifyDrawerCallback',
    'openAddInfoItemDrawerCallback',
    'openAddSystemDrawer',
    'refreshList'
  ],
  setup(props, { emit }) {
    const { proxy } = getCurrentInstance()

    const visible = computed({
      get: () => props.visible,
      set: (val) => {
        emit('update:visible', val)
      }
    })

    const handleClose = () => {
      visible.value = false
      state.auditRecord = []
      state.activeTab = 'info'
      state.checkedKeys = ''
      state.checkCatalogNameFlag = ''
      state.fromSystemList = []
      state.cataFormatList = [] // 信息资源格式列表
      state.isSwitchFormat = false
      state.list = [] // 信息项当前集合
      state.addItemList = [] // 新增的信息项当前集合
      state.editItemList = [] // 编辑的信息项当前集合
      state.delItemList = [] // 删除的信息项当前集合
      state.selectTableRow = []
      state.showDepartment = false
      state.titleActiveA = true
      state.titleActiveB = true
      state.titleActiveC = true
      state.titleActiveD = true
      state.showCategory = false
      state.showRelationType = false
      state.showAuthorizeUnitClearIcon = false
      Object.keys(formState).forEach((item) => {
        formState[item] = ''
      })
      formState.catalogType = '1'
      formState.shardType = undefined
      formState.sharindMethod = undefined
      formState.isCascade = '1'
      formState.isClassified = '0'
      formState.certificationType = '0'
      formState.isPrivateData = '0'
      formState.isHavingSystem = '1'
      formState.catalogAuthOrgs = [] // 授权单位
      formState.refCategoryIds = []
      formState.cancelReason = ''
      formState.cancelType = ''
      formState.systemName = ''
      formState.date = []
      formState.dataProvideMode = []
      courseList.value = []
      changeFormState.maintainReason = ''
      revokedFormState.cancelReason = ''
      revokedFormState.cancelType = ''
    }

    const state = reactive({
      activeTab: 'info',
      auditRecord: [],
      checkCatalogNameFlag: '', // 校验目录引用时目录分类是否有权限标识
      checkedKeys: '', // 目录分类/关联分类回显id
      fromSystemList: [],
      listLoading: false,
      cataFormatList: [], // 信息资源格式列表
      isSwitchFormat: false,
      list: [], // 信息项当前集合
      addItemList: [], // 新增的信息项当前集合
      editItemList: [], // 编辑的信息项当前集合
      delItemList: [], // 删除的信息项当前集合
      selectTableRow: [],
      showDepartment: false,
      titleActiveA: true,
      titleActiveB: true,
      titleActiveC: true,
      titleActiveD: true,
      showCategory: false,
      showRelationType: false,
      showAuthorizeUnitClearIcon: false,
      span: 8,
      type: 'add',
      formLoading: false,
      btnLoading: false
    })

    const formState = reactive({
      cataId: undefined,
      catalogType: '1',
      categoryName: '',
      sourceItemName: '',
      relateItemName: '',
      categoryId: '',
      relationTypeNames: '',
      refCategoryIds: [],
      isHavingSystem: '1',
      fromSystem: '',
      systemName: '',
      directoryName: '',
      resourceCode: '',
      isPrivateData: '0',
      providerDeptName: '',
      resourceProviderId: '',
      internalDep: '',
      fromPlatform: '',
      cataFormat: '',
      cataFormatSecondDesc: '',
      cataFormatFirst: '',
      cataFormatSecond: '',
      updateCycle: undefined,
      cataAbstract: '',
      shardType: undefined,
      sharindCondition: '',
      sharindConditionArr: [],
      sharindConditionArrToString: '',
      sharindMethod: undefined,
      openType: '',
      labelName: '',
      openApproach: '',
      openCondition: '',
      netType: '',
      isCascade: '1',
      isClassified: '0',
      certificationType: '1',
      useType: '',
      userDesc: '',
      domian: '',
      domianDesc: '',
      authorizeUnit: '',
      cancelReason: '',
      cancelType: '',
      maintainReason: '',
      date: [],
      areaRange: undefined,
      dataLevel: undefined,
      catalogAuthOrgs: [], // 授权单位
      dataProvideMode: [], // 数据提供方式
      physicalModuleName: '',
      physicalModuleId: '',
      physicalTableName: '',
      physicalTableId: ''
    })

    const rules = reactive({
      categoryName: [
        { required: true, message: '请选择目录分类', trigger: 'change' }
      ],
      isHavingSystem: [
        { required: true, message: '请选择是否存在来源系统', trigger: 'change' }
      ],
      fromSystem: [
        { required: true, message: '请选择来源系统', trigger: 'change' }
      ],
      areaRange: [
        { required: false, message: '请选择数据区域范围', trigger: 'change' }
      ],
      date: [
        { required: false, message: '请选择数据时间范围', trigger: 'change' }
      ],
      dataLevel: [
        { required: false, message: '请选择数据层级', trigger: 'change' }
      ],
      directoryName: [
        {
          required: true,
          trigger: 'blur',
          validator: async (rule, value, callback) => {
            if (!value) {
              callback(new Error('请输入目录名称'))
            } else {
              const res =
                await proxy.$api.bizApi.governmentInformationRegister.checkNameRepetition(
                  value,
                  formState.cataId,
                  null,
                  1
                )
              if (res.code === '200' || res.code === 200) {
                callback()
              } else {
                callback(new Error('该目录名称已存在,请重新输入'))
              }
            }
          }
        }
      ],
      labelName: [
        {
          trigger: 'blur',
          validator: (rule, value, callback) => {
            const reg = /^[\u4e00-\u9fa5,a-zA-Z0-9]+$/
            if (value) {
              if (!reg.test(value)) {
                return callback(new Error('只允许输入字母，数字及英文逗号'))
              } else {
                callback()
              }
            } else {
              callback()
            }
          }
        }
      ],
      resourceCode: [
        { required: true, message: '根据目录分类生成', trigger: 'change' }
      ],
      isPrivateData: [
        { required: true, message: '请选择是否涉密', trigger: 'change' }
      ],
      providerDeptName: [
        { required: true, message: '请输入资源提供方', trigger: 'change' }
      ],
      updateCycle: [
        { required: true, message: '请选择更新周期', trigger: 'change' }
      ],
      cataFormat: [
        {
          required: true,
          validator: (rule, value, callback) => {
            if (!value || !value.length) {
              callback(new Error('请选择信息资源格式'))
            } else if (state.isSwitchFormat) {
              let regZ = /^[\u4e00-\u9fa5]+$/
              let regC = /^[a-zA-Z0-9,]+$/
              if (
                value.indexOf('/') !== -1 &&
                value.split('/').length === 2 &&
                regZ.test(value.split('/')[0]) &&
                regC.test(value.split('/')[1])
              ) {
                formState.cataFormatFirst = value.split('/')[0]
                formState.cataFormatSecond = value.split('/')[1]
                callback()
              } else {
                callback(
                  new Error(
                    '手动输入格式(一级/二级1,二级2)如(数据库/Dm,dbf)组合'
                  )
                )
              }
            } else {
              callback()
            }
          },
          trigger: 'change'
        }
      ],
      cataAbstract: [
        { required: true, message: '请输入目录摘要', trigger: 'change' }
      ],
      netType: [
        { required: false, message: '请选择提供渠道', trigger: 'change' }
      ],
      shardType: [
        { required: true, message: '请选择共享类型', trigger: 'change' }
      ],
      sharindMethod: [],
      sharindCondition: [],
      sharindConditionArr: [
        { required: true, message: '请选择共享要求', trigger: 'change' }
      ],
      openType: [
        { required: true, message: '请选择开放类型', trigger: 'change' }
      ],
      openApproach: [],
      openCondition: [],
      certificationType: [
        { required: true, message: '请选择电子证照', trigger: 'change' }
      ],
      maintainReason: [
        { required: true, message: '请输入变更说明', trigger: 'blur' }
      ],
      isCascade: [
        { required: true, message: '请选择是否级联', trigger: 'change' }
      ],
      dataProvideMode: [
        { required: true, message: '请选择数据提供方式', trigger: 'change' }
      ]
    })

    const handleHeaderDrag = (newWidth, oldWidth, column, event) => {
      nextTick(function () {
        proxy.$refs.ItemsTable.doLayout()
      })
    }
    const selectMainTableRow = (selection) => {
      state.selectTableRow = Object.assign([], selection)
    }

    // 获取来源系统下拉
    const getPassStatus = async (val) => {
      const { code, data } =
        await proxy.$api.bizApi.governmentInformationRegister.getPassStatus()
      if (code === '200' || code === 200) {
        state.fromSystemList = data || []
        courseList.value = data || []
        if (val) {
          formState.fromSystem = data?.find(
            (item) => item.systemName === val
          )?.systemId
        }
      }
    }

    // 信息项按钮组
    const getInfoItemButtons = (row) => {
      return {
        row: row,
        buttons: [
          {
            label: '编辑',
            icon: 'edit',
            clickFn: handleEdit
          },
          {
            label: '删除',
            icon: 'delete',
            clickFn: handleDelete
          }
        ]
      }
    }

    // 信息项新增弹窗
    const handleAdd = () => {
      emit('openAddInfoItemDrawerCallback', 'add')
    }
    // 信息项编辑弹窗
    const handleEdit = (row) => {
      emit('openAddInfoItemDrawerCallback', 'edit', row.row)
    }

    const sharindConditionTitle = computed(() => {
      let title = '请输入共享要求'
      if (props.isOnlyRead) {
        return ''
      }
      let type = formState.shardType
      if (type === '1') {
        title =
          '说明使用要求（包括作为行政依据、作为工作参考、用于数据校核、用于业务协同等）'
      } else if (type === '2') {
        title =
          '说明共享要求、共享范围、使用要求（包括作为行政依据、作为工作参考、用于数据校核、用于业务协同等）'
      } else if (type === '3') {
        title = '说明相关的法律、行政法规或党中央、国务院政策依据'
      }
      return title
    })

    // 信息项删除
    const handleDelete = ({ row, $index }) => {
      let info = row ? `您确定要删除信息项"${row.itemName}"吗?` : ''
      if (state.selectTableRow.length === 0 && !info) {
        proxy.$notify({
          title: '提示',
          message: '请选择要删除的信息项',
          type: 'info',
          duration: 2000
        })
      } else {
        proxy
          .$confirm(info || '您确定要批量删除信息项吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            state.listLoading = true
            if (info) {
              state.list.splice($index, 1)
              state.delItemList.push(row.id)
              // 去除掉新增和编辑的信息项集合
              spliceAddOrEditItemList(row.id)
            }
            setTimeout(() => {
              state.listLoading = false
            }, 300)
          })
      }
    }

    // 信息项 新增/编辑回调
    const itemsAE = (info, currentRow) => {
      if (currentRow) {
        state.list.splice(currentRow.$index, 1, info)
        // proxy.$set(state.list, currentRow.$index, info);
      } else {
        state.list.push({ ...info })
      }
    }

    // 信息系统弹窗
    const systemAdd = () => {
      emit('openAddSystemDrawer')
      // proxy.$refs.addSystemDrawerRef.open("add");
    }
    // 信息目录/关联分类弹窗
    const openDirectory = (type) => {
      if (props.isOnlyRead) {
        return
      }
      let timer = new Date().getTime()
      state.checkedKeys =
        type === 1
          ? timer + '$' + formState.categoryId
          : timer + '$' + formState.refCategoryIds.toString()

      emit('openRelateClassifyDrawerCallback', type, state.checkedKeys)
      // proxy.$refs.Directory.open(type);
    }

    // 清除
    const cancel = () => {
      for (const k in formState) {
        formState[k] = ''
      }
      proxy.$refs['form'].resetFields()
      state.titleActiveA = state.titleActiveB = state.titleActiveC = true
      state.isSwitchFormat = false
      state.list = []
    }

    const cataFormatMap = {
      1: 'BM_FIRST_ELEC_FILE',
      2: 'BM_FIRST_FILE',
      3: 'BM_FIRST_DATABASE',
      4: 'BM_FIRST_IMAGE',
      5: 'BM_FIRST_STREAM',
      6: 'BM_FIRST_OTHER',
      7: 'BM_FIRST_INTERFACE'
    }
    // 获取信息资源格式
    const getDirectoryType = () => {
      let result = []
      proxy.$api.dict
        .getDictByType({ type: 'BM_DIRECTORY_FIRST' })
        .then((res) => {
          if (res.code === '200' || res.code === 200) {
            for (const k in res.data) {
              result.push({
                value: k,
                children: [],
                disabled: false,
                label: res.data[k]
              })
            }
          }
          result.forEach((item) => {
            proxy.$api.dict
              .getDictByType({ type: cataFormatMap[item.value] })
              .then((res) => {
                if (res.code === '200' || res.code === 200) {
                  item.children = Object.entries(res.data).map(
                    ([key, value]) => {
                      return {
                        label: value,
                        value: key
                      }
                    }
                  )
                }
              })
          })
          state.cataFormatList = result
        })
    }

    // 是否存在来源系统
    const changeSystem = (val) => {
      if (val === '0') {
        formState.fromSystem = ''
      }
    }

    const cleanCategory = (val) => {
      if (val === 1) {
        formState.categoryName =
          formState.categoryId =
          formState.resourceCode =
            ''
      } else if (val === 2) {
        formState.refCategoryIds = []
        formState.relationTypeNames = ''
      }
    }

    const getDetails = async (id, from) => {
      // 来源引用
      state.formLoading = true
      try {
        const res =
          state.type === 'historyView'
            ? await proxy.$api.bizApi.governmentInformationRegister.getHisListDetail(
                { isPrevVersion: '0', hisCataId: id }
              )
            : await proxy.$api.bizApi.governmentInformationRegister.getDetailById(
                id
              )
        if (res.code === '200' || res.code === 200) {
          res.data.catalogItemList.forEach((item) => {
            state.list.push({
              id: item.itemId,
              accuracy: item.accuracy,
              dataType: item.dataTypeCode,
              dataTypeCode: item.dataType,
              itemName: item.itemName,
              itemDesc: item.itemDesc,
              dataLength: item.dataLength,
              isPublic: item.isPublicCode,
              isPublicCode: item.isPublic,
              sensitivityLevel: item.sensitivityLevelCode,
              sensitivityLevelCode: item.sensitivityLevel,
              lastUpdatedBy: item.lastUpdatedBy,
              lastUpdatedTime: item.lastUpdatedTime,
              createdTime: item.createdTime,
              timeType: item.timeTypeCode,
              timeTypeCode: item.timeType,
              sort: item.sort
            })
          })
          Object.keys(formState).forEach(async (k) => {
            if (k === 'cataFormat') {
              formState[k] = res.data.catalogBasicInfo.cataFormatSecond
                ? res.data.catalogBasicInfo.cataFormatSecond
                    .split(',')
                    .map((item) => [item[0], item])
                : []
            } else if (k === 'resourceCode') {
              // 引用时候信息目录分类为空时 重置信息目录代码
              if (!res.data.catalogBasicInfo.categoryId) {
                formState[k] = ''
              } else if (from === 'quote' && res.data.catalogBasicInfo[k]) {
                // 目录引用 信息目录代码重置
                let randomList = []
                let indexList = Math.floor(Math.random() * 50)
                for (let i = 0; i < 50; i++) {
                  randomList.push(Math.floor(Math.random() * 1000))
                }
                const random = randomList[indexList]
                // 去除0
                let threeRandom = random || '001'
                if (String(threeRandom).length === 1) {
                  threeRandom = '00' + threeRandom
                } else if (String(threeRandom).length === 2) {
                  threeRandom = '0' + threeRandom
                }
                formState[k] =
                  res.data.catalogBasicInfo[k].split('/')[0] +
                  '/' +
                  formatWithSeperator(new Date(), '', '').replace(' ', '') +
                  threeRandom
              } else {
                formState[k] = res.data.catalogBasicInfo[k]
              }
            } else if (k === 'providerDeptName' && from === 'quote') {
              // 目录引用时 资源提供方用当前账号信息
              formState.providerDeptName =
                proxy.$store.state.user.currentUser.unitName || ''
              formState.resourceProviderId =
                proxy.$store.state.user.currentUser.unitId || ''
            } else if (k === 'directoryName' && from === 'quote') {
              // 目录引用时  信息目录名称清空
              formState[k] = ''
            } else if (k === 'labelName') {
              if (res.data.catalogRelationLabelList.length > 0) {
                for (const item of res.data.catalogRelationLabelList) {
                  formState[k] += item.labelName + ','
                }
                formState[k] = formState[k].slice(0, formState[k].length - 1)
              }
            } else if (k === 'categoryName') {
              formState[k] = res.data.catalogTypeInfo.name
              formState.categoryId = res.data.catalogTypeInfo.id
            } else {
              formState[k] = res.data.catalogBasicInfo[k]
            }
          })
          formState.dataProvideMode =
            res.data.catalogBasicInfo.dataProvideMode?.split(',')
          formState.catalogType = res.data.catalogBasicInfo.catalogType

          formState.refCategoryIds = res.data.relCatalogTypeList?.map(
            (item) => item.id
          )
          formState.systemName = res.data.publicSystem.systemName
          formState.relationTypeNames = res.data.relCatalogTypeList
            ?.map((item) => item.name)
            .join(',')
          formState.authorizeUnit = res.data.authOrgRelList
            ?.map((item) => item.unitName)
            .toString()

          formState.catalogAuthOrgs = res.data.authOrgRelList || []

          if (
            res.data.catalogBasicInfo.dataEnd &&
            res.data.catalogBasicInfo.dataBegin
          ) {
            formState.date = [
              res.data.catalogBasicInfo.dataBegin,
              res.data.catalogBasicInfo.dataEnd
            ]
          } else {
            formState.date = []
          }

          // 共享类型 查看详情切换是否必填字段
          if (formState.shardType === '2') {
            formState.sharindConditionArr =
              res.data.catalogBasicInfo.sharindCondition?.split(',') || []

            if (
              res.data.catalogBasicInfo.sharindCondition &&
              res.data.catalogBasicInfo.sharindCondition
                .split(',')
                .every((i) => /^\d+$/.test(i))
            ) {
              // formState.sharindConditionArrToString = formState.sharindConditionArr
              //   .map((item) => {
              //     return shardType.value.find((val) => val.value === item).label
              //   })
              //   .toString()
            } else {
              if (from === 'quote') {
                formState.sharindConditionArr = []
              }
            }
          }

          // 开放类型 查看详情切换是否必填字段
          if (formState.openType === '1') {
            rules.openApproach = [
              { required: true, message: '请输入开放方式', trigger: 'change' }
            ]
          } else {
            rules.openApproach = []
            formRef.value.clearValidate('openApproach')
          }

          // 来源为引用的特殊处理
          if (from === 'quote') {
            formState.cataId = ''
            formState.fromSystem = ''
            // formState.fromSystem =
            //   res.data.catalogBasicInfo.deptId ===
            //   proxy.$store.state.user.currentUser.unitId
            //     ? formState.fromSystem
            //     : ''
            formState.authorizeUnit = ''
            formState.catalogAuthOrgs = []
            formState.categoryName = ''
            formState.categoryId = ''
            formState.refCategoryIds = []
            formState.relationTypeNames = ''

            // if (formState.catalogType !== '1') {
            //   formState.categoryName = ''
            //   formState.categoryId = ''
            //   formRef.value.clearValidate('categoryName')
            // } else {
            //   await checkCatalogName(res.data.catalogTypeInfo.id)

            //   if (state.checkCatalogNameFlag === res.data.catalogTypeInfo.id) {
            //     formState.categoryName = res.data.catalogTypeInfo.name
            //     formState.categoryId = res.data.catalogTypeInfo.id
            //   }
            // }

            // 引用的目录类型始终为信息目录
            formState.catalogType = '1'
          }

          let currentIndex = courseList.value.findIndex(
            (item) => item.systemId === formState.fromSystem
          )
          if (currentIndex !== -1 && currentIndex !== 0) {
            let temp = state.fromSystemList.splice(currentIndex, 1)[0] // 先删除元素并保存
            state.fromSystemList.splice(0, 0, temp) // 在指定位置插入元素
          }

          nextTick(() => {
            formRef.value.clearValidate()
          })
        }
        state.formLoading = false
      } catch (e) {
        console.log(e)
        state.formLoading = false
      }
    }

    const openDrawer = (type, info) => {
      visible.value = true
      state.type = type
      getDirectoryType()
      if (type !== 'add') {
        if (type === 'quote') {
          getDetails(info.cataId, type)
        } else {
          formState.cataId = info.cataId
          getDetails(type === 'historyView' ? info.cataHisId : info.cataId)
        }
        if (type === 'maintenance') {
          formState.maintainReason = ''
        }
      }
      if (type === 'add') {
        nextTick(function () {
          if (info) {
            formState.isHavingSystem = '1'
            formState.fromSystem = info.fromSystem
          }
          let currentUser = proxy.$store.state.user.currentUser || ''
          formState.providerDeptName = currentUser?.unitName
          formState.resourceProviderId = currentUser?.unitId
          formRef.value.clearValidate()
        })
      }

      if (['historyView', 'view'].includes(type)) {
        getAuditRecord(info.cataId)
      }
    }

    // type = 1 仅保存; type = 2 保存并提交
    const save = async (type) => {
      try {
        await formRef.value.validate()
        if (type === 1) {
          await saveApi(type, '保存成功')
        } else if (type === 2) {
          await saveApi(type, '提交成功')
        }
      } catch (e) {
        console.log(e)
        state.titleActiveA = true
        state.titleActiveB = true
        state.titleActiveC = true
      }
    }

    const saveApi = throttle(
      async (type, info) => {
        try {
          state.formLoading = true
          state.btnLoading = true
          let tempData = JSON.parse(JSON.stringify(formState))
          tempData.dataProvideMode = tempData.dataProvideMode.toString()

          // 如果为引用-删除id
          if (state.type === 'quote') {
            delete tempData.cataId
          }
          if (typeof tempData.cataFormat !== 'string') {
            tempData.cataFormatFirst = tempData.cataFormat[0][0]
            tempData.cataFormatSecond = tempData.cataFormat
              .map((item) => item[1])
              .join(',')
          }
          delete tempData.cataFormat
          tempData.catalogItems = []
          tempData.editCatalogItems = []
          tempData.delCatalogItemIds = []
          if (state.list.length > 0) {
            // 处理字段，防止保存
            if (state.type === 'quote') {
              // 目录通过引用新增过来的，信息项list默认全部都是新增的
              let addList = JSON.parse(JSON.stringify(state.list))
              addList.forEach((item, index) => {
                delete addList[index].id
                delete addList[index].dataTypeCode
                delete addList[index].isPublicCode
                delete addList[index].lastUpdatedBy
              })
              tempData.catalogItems = addList
            } else {
              let addList = JSON.parse(JSON.stringify(state.addItemList))
              addList.forEach((item, index) => {
                delete addList[index].id
                delete addList[index].dataTypeCode
                delete addList[index].isPublicCode
                delete addList[index].lastUpdatedBy
              })
              let editList = JSON.parse(JSON.stringify(state.editItemList))
              editList.forEach((item, index) => {
                editList[index].itemId = editList[index].id
                delete editList[index].id
                delete editList[index].dataTypeCode
                delete editList[index].isPublicCode
                delete editList[index].lastUpdatedBy
              })
              tempData.catalogItems = addList
              tempData.editCatalogItems = editList
              tempData.delCatalogItemIds = state.delItemList
            }
          } else {
            proxy.$notify({
              title: '提示',
              message: '未输入信息项内容',
              type: 'info'
            })
            state.btnLoading = false
            state.formLoading = false
            return
          }
          if (tempData.shardType === '2') {
            tempData.sharindCondition = tempData.sharindConditionArr.toString()
          }
          if (tempData.date.length) {
            tempData.dataBegin = tempData.date[0]
            tempData.dataEnd = tempData.date[1]
          }
          delete tempData.date

          let types = 'save' // 新增接口
          if (type === 1 && state.type === 'edit') {
            types = 'edit' // 编辑接口
          }
          if (type === 2 && (state.type === 'add' || state.type === 'quote')) {
            types = 'saveAndSubmit' // 新增并送审
          }
          if (type === 2 && state.type === 'edit') {
            types = 'editAndSubmit' // 保存并送审
          }

          // eslint-disable-next-line
          const res = await proxy.$api.bizApi.governmentInformationRegister[
            types
          ](tempData)
          if (res.code === '200' || res.code === 200) {
            if (state.type === 'quote') {
              emit('quoteUpdate')
            }
            proxy.$message.success(info)
            state.btnLoading = false
            state.formLoading = false
            handleClose()
            emit('refreshList')
          } else {
            proxy.$message.error(res.message)
          }
          state.btnLoading = false
          state.formLoading = false
        } catch (e) {
          state.btnLoading = false
          state.formLoading = false
          console.log(e)
        }
      },
      1000,
      {
        leading: true,
        trailing: false
      }
    )

    // 变更
    const formRef = ref(null)
    const maintenance = throttle(
      async () => {
        try {
          await formRef.value.validate()
          await changeFormRef.value.validate()

          let tempData = JSON.parse(JSON.stringify(formState))
          tempData.dataProvideMode = tempData.dataProvideMode.toString()
          if (typeof tempData.cataFormat !== 'string') {
            tempData.cataFormatFirst = tempData.cataFormat[0][0]
            tempData.cataFormatSecond = tempData.cataFormat
              .map((item) => item[1])
              .join(',')
          }
          delete tempData.cataFormat
          tempData.catalogItems = []
          tempData.editCatalogItems = []
          tempData.delCatalogItemIds = []
          if (state.list.length > 0) {
            // 处理字段，防止保存
            delete tempData.cataFormat
            // 处理字段，防止保存
            let addList = JSON.parse(JSON.stringify(state.addItemList))
            addList.forEach((item, index) => {
              delete addList[index].id
              delete addList[index].dataTypeCode
              delete addList[index].isPublicCode
              delete addList[index].lastUpdatedBy
            })
            let editList = JSON.parse(JSON.stringify(state.editItemList))
            editList.forEach((item, index) => {
              editList[index].itemId = editList[index].id
              delete editList[index].id
              delete editList[index].dataTypeCode
              delete editList[index].isPublicCode
              delete editList[index].lastUpdatedBy
            })
            tempData.catalogItems = addList
            tempData.editCatalogItems = editList
            tempData.delCatalogItemIds = state.delItemList
          } else {
            proxy.$notify({
              title: '提示',
              message: '未输入信息项内容',
              type: 'info'
            })
            return
          }
          if (tempData.date.length) {
            tempData.dataBegin = tempData.date[0]
            tempData.dataEnd = tempData.date[1]
          }
          if (tempData.shardType === '2') {
            tempData.sharindCondition = tempData.sharindConditionArr.toString()
          }
          tempData.maintainReason = changeFormState.maintainReason
          state.formLoading = true
          state.btnLoading = true
          const res =
            await proxy.$api.bizApi.governmentInformationRegister.maintenance(
              tempData
            )

          if (res.code === '200' || res.code === 200) {
            proxy.$message.success(res.message)
            state.formLoading = false
            state.btnLoading = false
            handleClose()
            emit('refreshList')
          } else {
            proxy.$message.error(res.message)
          }
        } catch (e) {
          state.btnLoading = false
          state.formLoading = false
          state.titleActiveA = true
          state.titleActiveB = true
          state.titleActiveC = true
        }
      },
      1000,
      {
        leading: true,
        trailing: false
      }
    )
    const handleOpenAuthorizeUnitDrawer = () => {
      emit('openAuthorizeUnitDrawerCallback', formState.catalogAuthOrgs)
    }

    // 控制信息资源格式多选配置
    const cascaderProps = { multiple: true }
    // 限制信息资源格式一级单选二级多选
    const handleCascaderChange = (val) => {
      if (val.length > 0) {
        formState.cataFormatFirst = [
          ...new Set(val.map((item) => item[0]))
        ].join()
        formState.cataFormatSecond = val.map((item) => item[1]).join()
      } else {
        formState.cataFormatFirst = formState.cataFormatSecond = ''
      }
    }
    // 改变开放类型事件
    const changeOpenType = (val) => {
      if (val?.length && (val[0] === '3' || val[0] === '1')) {
        rules.openApproach = [
          { required: true, message: '请输入开放方式', trigger: 'change' }
        ]
        rules.openCondition = [
          { required: true, message: '请输入开放要求', trigger: 'blur' }
        ]
      } else {
        rules.openApproach = []
        rules.openCondition = []
        formRef.value.clearValidate('openApproach')
        formRef.value.clearValidate('openCondition')
      }
    }

    // 改变共享类型
    const changeShardType = (val) => {
      formState.sharindCondition = ''
      formState.sharindConditionArr = []
      if (val?.length && val[0] !== '2') {
        formState.sharindCondition = ''
        formRef.value.clearValidate('sharindConditionArr')

        if (val[0] === '1') {
          rules.sharindMethod = [
            { required: true, message: '请选择共享方式', trigger: 'change' }
          ]
        }
        if (val[0] === '3') {
          rules.sharindMethod = []
          formRef.value.clearValidate('sharindMethod')
        }
      } else {
        rules.sharindMethod = [
          { required: true, message: '请选择共享方式', trigger: 'change' }
        ]
      }
    }
    const spliceAddOrEditItemList = (id) => {
      let indexAdd = state.addItemList.findIndex((item) => item.id === id)
      let indexEdit = state.editItemList.findIndex((item) => item.id === id)

      if (indexAdd !== -1) {
        state.addItemList.splice(indexAdd, 1)
      }

      if (indexEdit !== -1) {
        state.editItemList.splice(indexEdit, 1)
      }
    }
    const shardType = ref([])
    const queryShardType = async () => {
      const { code, data } = await proxy.$api.dict.getDictByType({
        type: 'BM_SHARIND_CONDITION'
      })
      if (code === '200') {
        shardType.value = Object.entries(data).map(([key, value]) => {
          return {
            label: value,
            value: key
          }
        })
      }
    }

    // 获取审核记录
    const getAuditRecord = async (id) => {
      try {
        const res = await proxy.$api.bizApi.auditing.queryAuditRecord(id)
        if (res.code === '200' || res.code === 200) {
          state.auditRecord = res.data
        }
      } catch (e) {
        console.log(e)
      }
    }

    // 日期组件快捷选项
    const pickerOptions = {
      shortcuts: [
        {
          text: '最近一天',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }
      ]
    }

    // const checkCatalogName = async (id) => {
    //   const { code, data } =
    //     await proxy.$api.bizApi.governmentInformationRegister.checkCatalogName(
    //       id
    //     )
    //   if (code === '200') {
    //     state.checkCatalogNameFlag = data.categoryId
    //   }
    // }

    const handleClearAuthorizeUnit = () => {
      formState.authorizeUnit = ''
      formState.catalogAuthOrgs = []
    }
    watch(
      () => visible.value,
      (val) => {
        if (val) {
          getPassStatus()
          queryShardType()
          handleQueryDataSourceProvideMode()
        }
      }
    )

    watch(
      () => formState.sharindConditionArr,
      (val) => {
        if (val && val.length && val.every((i) => /^\d+$/.test(i))) {
          formState.sharindConditionArrToString = formState.sharindConditionArr
            .map((item) => {
              return shardType.value.find((val) => val.value === item).label
            })
            .toString()
        }
      }
    )

    watch(
      () => state.activeTab,
      (val) => {
        if (val === 'process') {
          nextTick(() => {
            proxy.$refs.progressRef.handleOpened(formState.cataId)
          })
        }
      }
    )

    /** 懒加载来源系统 --------开始------------------ */
    const rangeNumber = ref(10)
    const timer = ref(null)
    const searchLoad = ref(false)
    const courseList = ref([])
    const loadMore = (n) => {
      return () => (rangeNumber.value += 5)
    }

    // 监听select下拉框的显示和隐藏
    const visibleChange = (flag) => {
      // 显示时初始化列表
      flag && filterMethod('') // 初始化默认值
      rangeNumber.value = 10
    } // 过滤课件

    const filterMethod = (query) => {
      if (timer.value != null) clearTimeout(timer.value)
      !searchLoad.value && (searchLoad.value = true)
      timer.value = setTimeout(() => {
        state.fromSystemList = query
          ? courseList.value.filter((el) => el.systemName.includes(query))
          : courseList.value
        clearTimeout(timer.value)
        searchLoad.value = false
        rangeNumber.value = 10
        timer.value = null
      }, 500)
    }

    /** 懒加载来源系统 --------结束------------------ */

    // 数据提供方式
    const dataProvideModeDict = ref([])
    const handleQueryDataSourceProvideMode = async () => {
      const { code, data } = await proxy.$api.dict.getDictByType({
        type: 'ZYML_DATA_PROVIDE_MODE'
      })
      if (code === '200') {
        dataProvideModeDict.value = Object.entries(data).map(([key, value]) => {
          return {
            label: value,
            value: key
          }
        })
      }
    }

    const mapRule = {
      1: '1',
      2: '10',
      3: '9',
      4: '2',
      5: '1',
      6: '4',
      7: '5',
      8: '13',
      9: '12',
      字符串型: '字符串型C',
      整型: '整型I',
      双精度型: '双精度型B',
      高精度型: '数值型N',
      布尔型: '字符串型C',
      日期时间型: '日期型D',
      时间戳型: '日期时间型T',
      长文本clob: '长文本text',
      二进制blob: '二进制blob'
    }

    const mapRule2 = {
      1: '字符串型C',
      2: '整型I',
      3: '双精度型B',
      4: '数值型N',
      5: '字符串型C',
      6: '日期型D',
      7: '日期时间型T',
      8: '长文本text',
      9: '二进制blob'
    }

    const queryInfoItemByEntityId = async (id) => {
      const { code, data } = await getInfoItemByEntityId({
        entityId: id
      })
      if (code === '200') {
        if (data && data.length) {
          state.list = state.addItemList = data.map((item, index) => {
            return {
              ...item,
              dataType: mapRule[item.dataType],
              dataTypeCode: mapRule2[item.dataType],
              sort:
                state.list && state.list.length
                  ? state.list[state.list.length - 1].sort + 1
                  : 0
            }
          })
        }
      }
    }
    const addModule = () => {
      emit('addModule')
    }

    const addModuleTable = () => {
      if (!formState.physicalModuleId) {
        proxy.$message.info('请先选择物理模型')
        return
      }
      emit('addModuleTable', {
        physicalModelId: formState.physicalModuleId,
        physicalModelName: formState.physicalModuleName,
        modelDsType: ''
      })
    }

    const chooseTable = () => {
      if (!formState.physicalModuleId) {
        proxy.$message.info('请先选择物理模型')
        return
      }
      emit('chooseTable', formState.physicalModuleId)
    }

    const chooseModule = () => {
      emit('chooseModule')
    }

    const handleSetMaintainReason = (val) => {
      changeFormState.maintainReason = val
    }

    // 变更表单
    const changeFormRef = ref(null)
    const changeFormState = reactive({
      maintainReason: ''
    })
    const changeFormRules = {
      maintainReason: [
        {
          required: true,
          message: '请输入变更说明',
          trigger: 'blur'
        }
      ]
    }

    const revokedFormRef = ref(null)
    const revokedFormState = reactive({
      cancelReason: '',
      cancelType: ''
    })
    const revokedFormRules = {
      cancelReason: [
        {
          required: true,
          message: '请输入撤销说明',
          trigger: 'blur'
        }
      ],
      cancelType: [
        {
          required: true,
          message: '请选择撤销理由',
          trigger: 'change'
        }
      ]
    }

    return {
      revokedFormRef,
      revokedFormState,
      revokedFormRules,
      formRef,
      changeFormRef,
      changeFormState,
      changeFormRules,
      ...toRefs(state),
      handleSetMaintainReason,
      searchLoad,
      rangeNumber,
      loadMore,
      visibleChange,
      filterMethod,
      cascaderProps,
      formState,
      pickerOptions,
      props,
      visible,
      proxy,
      rules,
      sharindConditionTitle,
      getPassStatus,
      getInfoItemButtons,
      handleAdd,
      itemsAE,
      dataProvideModeDict,
      handleClose,
      handleHeaderDrag,
      selectMainTableRow,
      systemAdd,
      shardType,
      openDirectory,
      cancel,
      getDirectoryType,
      changeSystem,
      cleanCategory,
      openDrawer,
      queryInfoItemByEntityId,
      save,
      maintenance,
      changeOpenType,
      changeShardType,
      getAuditRecord,
      handleCascaderChange,
      handleOpenAuthorizeUnitDrawer,
      handleClearAuthorizeUnit,
      chooseModule,
      chooseTable,
      addModule,
      addModuleTable
    }
  }
})
</script>
<style lang="scss">
.height100 {
  height: 100%;
}

.fromSystemSelect {
  width: 200px !important;
  /deep/ .el-select-dropdown__item {
    width: 200px !important;
  }
}
</style>

<style scoped lang="scss">
/deep/ .el-textarea .el-input__count {
  bottom: -3px;
  right: 26px;
}

/deep/ .scrollbar-wrapper {
  .el-scrollbar__wrap {
    .el-scrollbar__view {
      padding-right: 12px;
    }
  }
}

.item {
  margin-left: 0 !important;
}
</style>
