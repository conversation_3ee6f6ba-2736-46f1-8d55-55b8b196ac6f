/**
 * 系统参数设置
 */
const config = {
  profile: 'dev',
  // 业务系统编码
  systemCode: 'zyml',
  // nacos应用名称
  appCode: 'catalog-wxw-31',
  appCode_gxsl: 'gxslProvince',
  appCode_auth: 'auth',
  appCode_uims: 'admin',
  client_id: 'app',
  // 指定用户cookie名称
  cookieName: 'thinventToken',
  // 设置系统默认皮肤（thinvent、thinvent_darken、orange、blue、green）
  css: 'blue',
  // 设置登录页面风格 jian表示吉安登录背景；thinvent表示统一用户登录背景 shzl表示社会治理背景
  loginBg: 'thinvent',
  // 导航菜单栏模式：1纵向导航；2横向导航
  navbar_mode: '1',
  // full为单页面模式，不配为tab模式
  // page_mode: 'full',
  // 首页图标，不设置采用默认的
  // homeIcon: 'spmsHome',
  // 是否显示快捷工具栏
  showRightPanel: false,
  // 平台名称（用于登录页面显示） 浏览器tab页显示内容
  plat_title: '资源目录平台',
  // JXYM: 江西云上密码
  certProvider: 'JXYM',
  // 登录页版权文字
  copyright: 'Copyright © 思创数码科技股份有限公司 版权所有',
  // 登录方式
  loginMode: {
    account: true,
    mobile: false,
    email: false,
    qq: false,
    wx: false,
    sina: false
  },
  // 登录短信验证码发送间隔时间
  codeDuration: 120,
  // 是否开启登陆警示标语
  warnMessage: true,
  // 是否是统一用户框架系统， false为中台框架系统
  isOnUims: true,
  // 无权限的按钮是否可见：1-可见；0-不可见
  isShowNoPermBtn: 1,
  // 错误信息提醒方式配置
  errMessage: {
    // API异常信息提醒方式：false-框架统-提醒，true-业务自定
    isCustomShowError: true,
    // API异常信息提醒时，是否允许显示异常信息：true-允许，false-不允许
    isShowErrorTrace: true
  },
  // 单点登录配置
  sso: {
    onoff: false, // true开启单点登录，false不开启单点登录
    url: 'http://192.168.2.163:7070/oauth/authorize', // 此处配置单点登录系统的系统选择页面的访问地址
    isCompatible: false, // 是否兼容老统一用户：false:不兼容，true:兼容
    logoutUrl: 'http://192.168.2.163:7070/#/login' // onoff: false时，本系统的登出地址
  },
  // 超时时间
  timeout: 600000,
  // 消息接收服务地址
  // 旧版示例：ws://192.168.2.80:5050/admin  新版示例：wsUrl: 'ws://192.168.194.149:8585/umsGroupChat'
  wsUrl: 'ws://192.168.202.226:9356/umsGroupChat',
  // 使用旧版的消息服务吗?，默认为false
  useOldMsgService: false,
  // 是否开启页面水印
  openWatermark: true,
  // RSA公钥
  rsaPub:
    'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmhRWFfGcRc0jX76zrTt8\n' +
    'n4nd/D6dpMt+HW+M39U6Awq2GtZwSQ71YhmTItf0MrpbN84uitGiXj5B2nhtf9It\n' +
    'Cb5fiw4QFw5skpytVrSlAHCgbybVXnvx7kl7+bPPLKF29EroMAOIglFXHFCPaM6m\n' +
    '1AkNf8kF13AOsfHo63LVx9cb/dMO+mv3ia5KEGuBPj5hXY0XO3v7oUFxjVJAdR3j\n' +
    'qO9b+vcAO6JzJgM5xu58YzcAhX8Jnmvs1inMDXKQhxauxs0B/rNApuMHQTw78eW7\n' +
    'aifzRQt/RBargqdqWd+SCzdkxSKcaGSkesNqoX2/V7kuxl+WeIFg5FkyK588Bj3a\n' +
    '0wIDAQAB',
  // RAS公钥切换: 0 默认为当前rsaPub配置; 1 从远程获取
  rsaSwitch: 1,
  // 重载页面是否一次性加载所有字典到页面上 1 是 0 否
  initAllDict: 1,
  // 系统跳转模式，配置为1/true表示使用ticket模式，否则使用token模式
  ticketModel: true,
  // 开关：404时自动跳首页
  no404: true,
  // 是否显示赣政通业务消息配置 false 否 true 是
  gztSwitch: true,
  // token失效处理模式，login 弹出登录窗口;logout 直接退出;refresh 自动刷新
  tokenTimeOutMode: 'login',
  customConfig: {
    // 省中心模式 用户管理创建用户不显示用户类型
    model: 'sxxzx'
  },
  headerBar: {
    // 系统列表
    system: {
      show: true, // 是否显示
      listTitle: ''
    },
    // logo区域
    logo: {
      show: true, // 是否显示logo
      logoUrl: '', // logoUrl: 不配用代码中默认的，指定风格下的logo图标
      top_title: '资源目录V3.1', // 平台名称（用于登录后TOP区显示）
      title: '' // 子系统名称
    },
    // 用户信息
    userInfo: {
      show: true, // 是否显示
      showThemeConfig: false, // 是否显示主题切换
      showChangePassword: true, // 是否显示密码修改
      showChangeUser: true, // 是否显示用户切换
      showModifyUser: true,
      showSafeUser: true
    },
    // 消息区域
    message: {
      show: true, // 是否显示消息区域
      showNotice: false, // 是否显示通知
      showMessage: true, // 是否显示站内消息
      showBusiness: true // 是否显示业务消息
    },
    // 关于区域
    about: {
      show: true // 是否显示关于区域
    }
  },
  // 皮肤范围（配置皮肤缩略图，名称，及各皮肤配套的底图）
  theme: {
    version: '2.0.5',
    thumbnails: [
      {
        label: '默认风格',
        name: 'thinvent',
        pictures: [
          { label: '无', name: 'nopic2' },
          { label: '流动数据', name: 'flow' },
          {
            label: '科技数据',
            name: 'science'
          },
          { label: '蜂窝数据', name: 'cellular' }
        ],
        picClass: 'bgColor'
      },
      {
        label: '深色风格',
        name: 'thinvent_darken',
        pictures: [
          { label: '无', name: 'nopic' },
          { label: '流动数据', name: 'flow' },
          {
            label: '科技数据',
            name: 'science'
          },
          { label: '蜂窝数据', name: 'cellular' }
        ]
      },
      {
        label: '橙色风格',
        name: 'orange',
        pictures: [
          { label: '无', name: 'nopic2' },
          { label: '流动数据', name: 'flow' },
          {
            label: '科技数据',
            name: 'science'
          },
          { label: '蜂窝数据', name: 'cellular' }
        ],
        picClass: 'bgColor'
      },
      {
        label: '蓝色风格',
        name: 'blue',
        pictures: [
          { label: '无', name: 'nopic2' },
          { label: '流动数据2', name: 'flow2' },
          {
            label: '科技数据2',
            name: 'science2'
          },
          { label: '蜂窝数据2', name: 'cellular2' }
        ]
      },
      {
        label: '浅色风格',
        name: 'green',
        pictures: [
          { label: '无', name: 'nopic2' },
          { label: '流动数据2', name: 'flow2' },
          {
            label: '科技数据2',
            name: 'science2'
          },
          { label: '蜂窝数据2', name: 'cellular2' }
        ]
      }
    ]
  },
  // 重置密码方式，不配置全部可用，配置了只允许使用配置的oldpwd 旧密码重置 sms 短信重置 email 邮件重置
  // changePwd: ['sms']
  // profile: 'dev'

  // bizConfig 业务系统配置
  // spaceId: '24dd952d5f86138f7b18f5a691eaf022', // 空间ID 开发环境
  spaceId: '9fa2dc37dea6ed03e78f10a4243ee928', // 空间ID
  catIdToDateModel: 'c7a942185050d0c87136f8fa7281f43b', // 建模目录分类ID
  iframeToDateModel:
    'http://192.168.120.22:10805/model/web/#/PhysicalModel/PhysicModelConfig', // 建模页面地址
  workFlowUrl: 'http://192.168.110.167:8182/tcwp/designer/demos/view.html', // 工作流地址
  // 共享门户1， 发改(住建)门户2
  portalType: 2,
  // 门户地址
  portalUrl: 'http://192.168.110.167:9201/igdp/#/portal/',
  defaultCronExpression: '0 0 * * * ?',
  databaseCatalogSubmitCallbackInfo:
    '目录数据已提交，2分钟后注册列表查看自检结果',
  buttonConfig: {
    approval: {
      label: '通过',
      color: '#1890ff'
    },
    reject: {
      label: '驳回',
      color: '#ff4d4f'
    }
  }
}
