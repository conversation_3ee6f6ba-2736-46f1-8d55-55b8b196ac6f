<template>
  <TDrawer
    :title="title"
    :visible.sync="show"
    size="24%"
    @close="cancel"
    :isDestroyWrapper="false"
  >
    <el-form ref="form" @submit.native.prevent size="small">
      <el-row :gutter="12">
        <el-col :span="20">
          <el-form-item label="" prop="filterText">
            <el-input
              class="clearStyle"
              @keyup.enter.native="query"
              v-model="filterText"
              placeholder="请输入分类名称"
              clearable
              :maxlength="30"
            />
          </el-form-item>
        </el-col>
        <el-col :span="3">
          <el-button size="mini" type="primary" @click="query">查询</el-button>
        </el-col>
      </el-row>
    </el-form>
    <el-tree
      :key="treeKey"
      :default-expand-all="treeExpand"
      class="treeList"
      ref="inBoxTree"
      v-loading="treeLoading"
      :props="defaultProps"
      :data="treeList"
      node-key="id"
      highlight-current
      show-checkbox
      check-strictly
      @check="handleCheck"
      @node-click="nodeClick"
      @node-expand="nodeClick"
      @node-collapse="nodeClick"
      :filter-node-method="filterNode"
      :default-expanded-keys="checkedKeys"
      :default-checked-keys="checkedKeys"
    >
      <span slot-scope="{ node }">
        <span :title="node.label">
          {{ node.label }}
        </span>
      </span>
    </el-tree>
    <template #footer>
      <el-button size="mini" class="cancel-btn" @click="cancel">取消</el-button>
      <el-button size="mini" type="primary" :loading="okLoading" @click="submit"
        >确认</el-button
      >
    </template>
  </TDrawer>
</template>

<script>
import TDrawer from 'biz/components/common/t-drawer'

export default {
  name: 'RelateClassifyDrawer',
  components: {
    TDrawer
  },
  props: {
    title: {
      type: String,
      default: '选择目录分类'
    },
    ids: String,
    onlyReflow: Boolean
  },
  watch: {
    // ids(val) {
    //   if (val) {
    //     if (val.split('$')[1] && val.split('$')[1] !== 'null') {
    //       // this.isSelect = false
    //       let sli = val.split('$')[1]
    //       this.checkedKeys = sli.split(',')
    //     } else {
    //       // this.isSelect = true
    //       this.checkedKeys = []
    //     }
    //   }
    // },
    show(val) {
      if (val && this.ids.length) {
        if (this.ids.split('$')[1] && this.ids.split('$')[1] !== 'null') {
          let sli = this.ids.split('$')[1]
          this.checkedKeys = sli.split(',')
        } else {
          this.checkedKeys = []
        }
      }
    }
  },
  data() {
    return {
      type: '',
      okLoading: false,
      lazy: true,
      treeKey: new Date().getTime(),
      treeExpand: false,
      checkedKeys: [],
      systemId: '',
      systemUintFilter: '0',
      treeList: [],
      radioSelect: '',
      checkSelect: '',
      treeLoading: false,
      show: false,
      filterText: '',
      isClearUp: false,
      defaultProps: {
        children: 'children',
        label: 'name',
        id: 'id',
        disabled: (data) => {
          return (
            data.childes ||
            data.childes > 0 ||
            data.children ||
            data.children > 0
          )
        },
        isLeaf: (data, node) => {
          return !(data.childes || data.childes > 0)
        }
      }
    }
  },
  methods: {
    // 懒加载
    loadNode(node, resolve) {
      if (node.level !== 0) {
        this.$api.bizApi.systemDirectory
          .getNodeAllByPid({
            pid: node.data.id,
            filterOffline: 'true',
            isPerms: 'true',
            unitId: this.$store.state.user.currentUser.unitId
          })
          .then((res) => {
            if (
              (res.code === '200' || res.code === 200) &&
              res.data &&
              res.data.length > 0
            ) {
              resolve(res.data.sort((a, b) => a.sort - b.sort))
            } else {
              resolve([])
            }
          })
          .catch(() => {
            resolve([])
          })
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    cancel() {
      this.isClearUp = false
      this.filterText = ''
      this.radioSelect = ''
      this.checkSelect = ''
      this.type = ''
      this.treeKey = new Date().getTime()
      this.lazy = true
      this.treeExpand = false
      this.$nextTick(() => {
        this.treeList = []
      })
      this.show = false
    },
    submit() {
      // if(this.isSelect) {
      //   this.$notify({
      //     title: '提示',
      //     message: '请选择内容',
      //     type: 'info',
      //     duration: 2000
      //   })
      //   return
      // }
      if (
        (this.type === 2 && !this.checkSelect) ||
        (this.type === 1 && !this.radioSelect)
      ) {
        if (this.isClearUp) {
          this.$emit('callBack', this.type, 'clearUp')
        }
        return this.cancel()
      }
      let currentName = ''
      let endListName = []
      const traverse = (node) => {
        if (
          typeof node.data === 'object' &&
          !Array.isArray(node.data) &&
          node.parent
        ) {
          currentName =
            node.data.name + (currentName ? '/' + currentName : currentName)
          traverse(node.parent)
        }
      }
      // 多选
      if (this.type === 2) {
        this.checkSelect.forEach((item) => {
          let node = this.$refs.inBoxTree.store.nodesMap[item]
          currentName = ''
          traverse(node)
          endListName.push(currentName)
        })
        this.$emit('callBack', this.type, {
          id: this.checkSelect.join(),
          name: endListName.join()
        })
      } else {
        // 单选
        let node = this.$refs.inBoxTree.store.nodesMap[this.radioSelect.id]
        currentName = ''
        traverse(node)
        this.$emit('callBack', this.type, {
          ...this.radioSelect,
          name: currentName
        })
      }
      this.cancel()
    },
    open(type) {
      this.show = true
      this.type = type
      this.getInBoxTree()
    },
    showWithDupParam(type, systemUintFilter, systemId) {
      this.systemUintFilter = systemUintFilter
      this.systemId = systemId
      this.open(type)
    },
    getInBoxTree() {
      this.treeLoading = true
      this.okLoading = true
      // eslint-disable-next-line standard/computed-property-even-spacing
      this.$api.bizApi.systemDirectory
        .querySystemDirectoryAll({ filterOffline: true, systemUintFilter: this.systemUintFilter, systemId: this.systemId })
        .then((res) => {
          this.treeLoading = false
          this.okLoading = false
          // console.log('res', res.data)
          if (res.code === '200' || res.code === 200) {
            if (this.onlyReflow && this.type === 1) {
              let treeData = []
              for (const item of res.data) {
                if (item.type === '9') {
                  treeData.push(item)
                }
              }
              this.treeList =
                (treeData || []) && treeData.sort((a, b) => a.sort - b.sort)
            } else {
              let treeData = []
              for (const item of res.data) {
                if (item.type !== '9') {
                  treeData.push(item)
                }
              }
              this.treeList =
                (treeData || []) && treeData.sort((a, b) => a.sort - b.sort)
            }
            this.nodeClick()
          }
        })
        .catch(() => {
          this.treeLoading = false
          this.okLoading = false
        })
    },
    handleCheck(data, checked) {
      if (this.type === 1) {
        this.$refs.inBoxTree.setCheckedKeys([data.id])
        if (checked.checkedKeys.length > 0) {
          this.isClearUp = false
          this.radioSelect = {
            id: data.id,
            name: data.name,
            preCode: data.preCode
          }
        } else {
          this.$refs.inBoxTree.setCheckedKeys([])
          this.isClearUp = true
          this.radioSelect = ''
        }
      } else {
        this.checkSelect = checked.checkedKeys
        this.isClearUp = checked.checkedNodes.length === 0
      }
    },
    nodeClick() {
      let num = 0
      let timer = setInterval(() => {
        num += 1
        // this.$refs.scrollbar.update();
        if (num === 3) {
          clearInterval(timer)
        }
      }, 300)
    },
    query() {
      if (!this.filterText) {
        this.treeKey = new Date().getTime()
        this.lazy = true
        this.treeExpand = false
        this.treeList = []
        return this.getInBoxTree()
      }
      this.treeList = []
      this.treeKey = new Date().getTime()
      this.lazy = false
      this.treeExpand = true
      this.treeLoading = true
      // this.$refs.inBoxTree.filter(this.filterText)
      this.$api.bizApi.systemDirectory
        .listAll({ name: this.filterText, filterOffline: 'true', systemUintFilter: this.systemUintFilter, systemId: this.systemId })
        .then((res) => {
          this.treeLoading = false
          if (res.code === '200' || res.code === 200) {
            if (this.onlyReflow) {
              let treeData = []
              for (const item of res.data) {
                if (item.type === '9') {
                  treeData.push(item)
                }
              }
              this.treeList =
                (treeData || []) && treeData.sort((a, b) => a.sort - b.sort)
            } else {
              this.treeList =
                res.data
                  .filter((item) => item.type !== '9')
                  .sort((a, b) => a.sort - b.sort) || []
            }
          }
          this.nodeClick()
        })
        .catch(() => {
          this.treeLoading = false
          this.nodeClick()
        })
    },
    // 清除
    cleanUp() {
      this.$refs.inBoxTree.setCheckedKeys([])
      this.isClearUp = true
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-form-item__content {
  display: flex;
}
.treeList /deep/ .is-disabled {
  display: none;
}
/deep/ .el-tree-node__content > span {
  overflow: hidden;
  text-overflow: ellipsis;
}
//重置
/deep/ .clearStyle.el-input--suffix .el-input__inner {
  padding-right: 30px;
}
</style>
