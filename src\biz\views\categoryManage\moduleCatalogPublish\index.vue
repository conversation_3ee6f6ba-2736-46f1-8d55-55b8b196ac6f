<template>
  <el-container class="page-container">
    <el-header>
      <div class="toolbar-wrapper" style="margin-bottom: 10px">
        <!-- <el-dropdown placement="bottom-start" @command="handleCommand">
          <perm-button label="新增" type="text" icon="add" />
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item icon="el-icon-thumb" command="a"
              >发布数据库目录</el-dropdown-item
            >

            <el-dropdown-item icon="el-icon-connection" command="c"
              >发布目录</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown> -->
        <PermButton
          label="发布"
          type="text"
          icon="add"
          @click="handlePublish"
        />
        <PermButton
          v-if="queryParams.flag === '1'"
          :disabled="selectTableRow.length === 0"
          label="批量发布"
          type="text"
          icon="uims-icon-commit"
          @click="bacthConfirmPublish"
        />
        <perm-button
          v-if="queryParams.flag === '1'"
          label="下载模板"
          type="text"
          icon="download"
          @click="handleDownload"
        />
        <!-- <perm-button
          v-if="queryParams.flag === '1'"
          label="导入目录"
          type="text"
          icon="uims-icon-export"
          @click="handleInport"
        /> -->
        <el-upload
          ref="uploadFile"
          action="#"
          accept=".xlsx"
          :http-request="handleInportService"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :show-file-list="false"
        >
          <TButton
            v-if="queryParams.flag === '1'"
            slot="trigger"
            label="导入目录"
            type="text"
            icon="uims-icon-upload"
          />
        </el-upload>
        <perm-button
          v-if="queryParams.flag === '1'"
          label="导出"
          type="text"
          icon="uims-icon-export"
          @click="handleExportRes"
        />
      </div>
      <el-tabs
        class="smart_city__el-tabs-biz"
        v-model="queryParams.flag"
        @tab-click="tabChange"
      >
        <el-tab-pane label="数据目录" name="1"></el-tab-pane>
        <el-tab-pane label="快速发布数据库目录" name="2"></el-tab-pane>
      </el-tabs>
      <el-form :inline="true" :model="queryParams" size="mini">
        <el-form-item label="目录名称">
          <el-input
            v-model.trim="queryParams.directoryName"
            clearable
            placeholder="请输入目录名称"
            @change="handleFilter"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="所属系统">
          <el-select
            @change="handleFilter"
            v-model="queryParams.systemId"
            filterable
            clearable
            placeholder="请选择所属系统"
            :loading="systemLoading"
          >
            <el-option
              v-for="item in systemOptions"
              :key="item.systemId"
              :label="item.systemName"
              :value="item.systemId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="目录状态" v-if="queryParams.flag === '1'">
          <SelectPlus
            dictType="CATALOG_REAL_STATUS"
            v-model="queryParams.status"
            clearable
            @change="handleFilter"
            placeholder="请选择目录状态"
          />
          <!-- <el-select
            v-model="queryParams.status"
            clearable
            @change="handleFilter"
            placeholder="请选择目录状态"
          >
            <el-option label="待发布" value="1"></el-option>
            <el-option label="发布审核中" value="2"></el-option>
            <el-option label="已发布" value="3"></el-option>
            <el-option label="发布审批拒绝" value="4"></el-option>
            <el-option label="变更待审批" value="5"></el-option>
            <el-option label="变更审批拒绝" value="6"></el-option>
          </el-select> -->
        </el-form-item>
        <el-form-item label="发布时间" v-if="queryParams.flag === '1'">
          <el-date-picker
            style="width: 280px"
            v-model="queryParams.date"
            type="daterange"
            range-separator="至"
            @change="handleFilter"
            value-format="yyyy-MM-dd"
            placeholder="请选择发布时间"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item
          label="是否注册数据目录"
          v-show="queryParams.flag === '2'"
        >
          <SelectPlus
            dictType="ZYML_IS_FLAG"
            v-model="queryParams.relDataCatalogFlg"
            clearable
            @change="handleFilter"
            placeholder="请选择"
          />
        </el-form-item>
        <perm-button
          label="查询"
          type="primary"
          icon="uims-icon-query"
          @click="getList"
        />
        <perm-button
          style="margin-left: 10px"
          type="primary"
          label="重置"
          icon="uims-icon-reset"
          @click="resetFilter"
        />
      </el-form>
    </el-header>
    <el-main>
      <TablePlus
        id="moduleCatalogPublishTable"
        :key="'moduleCatalogPublishTable' + queryParams.flag"
        ref="tableRef"
        :data="results.records"
        v-loading="loading"
        border
        fit
        stripe
        highlight-current-row
        @row-click="clickRow"
        @selection-change="selectMainTableRow"
        @header-dragend="handleHeaderDrag"
      >
        <template v-if="queryParams.flag === '1'">
          <el-table-column type="selection" width="60" align="center">
          </el-table-column>
          <el-table-column
            prop="directoryName"
            label="目录名称"
            min-width="200"
            fixed="left"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <span
                @mouseover="
                  (event) => {
                    event.target.style.textDecoration = 'underline'
                    event.target.style.color = '#0e88eb'
                    event.target.style.cursor = 'pointer'
                  }
                "
                @mouseout="
                  (event) => {
                    event.target.style.textDecoration = 'none'
                    event.target.style.color = '#606266'
                    event.target.style.cursor = 'default'
                  }
                "
                @click.stop="handleDetail(row)"
                >{{ row.directoryName }}</span
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="providerDeptName"
            label="部门名称"
            min-width="160"
            show-overflow-tooltip
          ></el-table-column>

          <el-table-column
            prop="fromSystem"
            label="所属系统"
            min-width="200"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="realCatalogStatus"
            label="目录状态"
            width="160"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <el-tag
                size="mini"
                :type="databaseCatalogBgColor(row.realCatalogStatusCode)"
                >{{ row.realCatalogStatus }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="realChangeStatus"
            label="目录变更状态"
            width="140"
            align="center"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="dbCount"
            label="资源数"
            width="160"
            align="right"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="publishTime"
            label="发布时间"
            width="140"
            align="center"
            show-overflow-tooltip
          ></el-table-column>
        </template>
        <template v-else>
          <el-table-column
            prop="directoryName"
            label="数据库目录名称"
            min-width="210"
            fixed="left"
            show-overflow-tooltip
          ></el-table-column>

          <el-table-column
            prop="providerDeptName"
            label="部门名称"
            min-width="180"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="fromSystem"
            label="所属系统"
            min-width="210"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="modelTotal"
            label="模型数"
            min-width="80"
            align="right"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="modelTableTotal"
            label="库表数"
            min-width="80"
            align="right"
            show-overflow-tooltip
          ></el-table-column>
        </template>

        <el-table-column
          label="操作"
          align="left"
          fixed="right"
          :width="queryParams.flag === '1' ? 320 : 100"
          class-name="smart_city-table-operate-cell"
        >
          <template slot-scope="{ row }">
            <perm-button-group :config="getButtons(row)" />
          </template>
        </el-table-column>
      </TablePlus>
    </el-main>
    <el-footer>
      <TableFooter
        ref="tableFooter"
        :showToolBar="true"
        excelName="数据目录"
        :showPage="true"
        :tableRef="tableRef"
        @sizeChange="handleSizeChange"
        @currentChange="handleCurrentChange"
        :currentPage="queryParams.currentPage"
        :pageSizes="[10, 20, 50, 100]"
        :pageSize="queryParams.pageSize"
        :total="results.total"
      >
      </TableFooter>
    </el-footer>

    <!-- 注册模型目录 -->
    <AddModuleCatalogDrawer
      ref="addDrawerRef"
      @openAddSystemDrawer="openAddSystemDrawer"
      @chooseModule="openChooseModuleSystem('module')"
      @addCallback="getList"
      @editInfo="editInfo"
      @openOrderDialog="openOrderDialog"
      @openResourceList="handleOpenResourceList"
      @openAddInfoItemDrawerCallback="openAddInfoItemDrawerCallback"
      @openRelateClassifyDrawerCallback="openRelateClassifyDrawerCallback"
    />

    <!-- 选择模型 -->
    <ChooseModuleSystem ref="moduleSystemRef" @handleNext="handleNext" />

    <!-- 信息系统弹窗 -->
    <AddSystemDrawer ref="addSystemDrawerRef" @update="getPassStatus" />

    <!-- 信息目录分类弹窗 -->
    <RelateClassifyDrawer
      title="选择目录分类"
      ref="relateClassifyDrawerRef"
      :ids="checkedKeys"
      @callBack="directoryBack"
    />

    <!-- 新增模型表 -->
    <AddModelTableDialog
      ref="addModelTableRef"
      :visible.sync="addModelTableVisible"
      @closeDialog="initModelTableCallback"
    />

    <!-- 融合新增 -->
    <AddModelByCurrentDrawer ref="addModelByCurrentRef" @nextStep="nextStep" />

    <!-- 信息项弹窗-->
    <AddInfoItemDrawer
      ref="addInfoItemDrawerRef"
      @itemsAE="itemsAE"
      :currentList="infoItemList"
    />

    <HistoryVersion
      :visible.sync="historyVersionVisible"
      :rowCataId="rowCataId"
      type="dataCategory"
      @handleDetail="handleHistoryDetail"
    />

    <GobackReasonDialog
      ref="gobackReasonDialogRef"
      :visible.sync="gobackReasonDialogVisible"
      :cataId="cataId"
      type="fgDataCatalog"
      @confirmCallback="confirmCallback"
    />

    <OrderdResListDialog
      ref="orderdResListRef"
      @gobackCheckStatus="gobackCheckStatus"
    />
  </el-container>
</template>

<script setup>
import { reactive, ref, nextTick, getCurrentInstance, onActivated } from 'vue'
import PermButton from '@/core/components/PermButton'
import PermButtonGroup from '@/core/components/PermButtonGroup'
import TablePlus from '@/core/components/TablePlus'
import TableFooter from '@/core/components/TablePlus/TableFooter'
import SelectPlus from '@/core/components/SelectPlus'
import AddModuleCatalogDrawer from '../components/registerModelCatalogDrawer'
import ChooseModuleSystem from '../components/chooseModuleSystem'
import AddModelTableDialog from '../components/addModelTableDialog'
import AddSystemDrawer from '../components/addSystemDrawer'
import RelateClassifyDrawer from '../components/relateClassifyDrawer'
import AddInfoItemDrawer from '../components/addInfoItemDrawer'
import HistoryVersion from '../components/historyVersion'
import AddModelByCurrentDrawer from '../components/addModelByCurrentDrawer'
import GobackReasonDialog from '../components/gobackReasonDialog'
import OrderdResListDialog from '../../resourceHook/components/orderdResListDialog.vue'
import {
  queryPublishModelCatalogList,
  modelCatalogPublish,
  cancelDataCatalog,
  querySupplyList,
  revokeModelCatalog,
  exportFgCatalog,
  publishCatalogTemplateDownload,
  importPublishCatalog
} from 'biz/http/api'
import Cookies from 'js-cookie'
import { databaseCatalogBgColor } from '@/biz/utils/utils'
import { isEmpty } from 'lodash-es'
import { FileUtils } from '@/utils/download'
import TButton from 'biz/components/common/t-button'
import moment from 'moment'

const { proxy } = getCurrentInstance()

const tableRef = ref(null)
const selectTableRow = ref([])
const clickRow = (row) => {
  tableRef.value.toggleRowSelection(row)
}
const selectMainTableRow = (selection) => {
  selectTableRow.value = Object.assign([], selection)
}

const addDrawerRef = ref(null)
const queryParams = reactive({
  currentPage: 1,
  pageSize: 20,
  status: '',
  isPublish: '1',
  directoryName: '',
  date: [],
  systemId: '',
  flag: '1',
  relDataCatalogFlg: ''
})
const results = reactive({
  records: [],
  total: 0
})

const getButtons = (row) => {
  if (queryParams.flag === '2') {
    return {
      row,
      buttons: [
        {
          label: '发布',
          icon: 'uims-icon-commit',
          clickFn: publishDatabaseCatlaog
        }
      ],
      showNums: 2
    }
  }
  let buttons = []

  if (row.realCatalogStatusCode === '4') {
    buttons.push({
      label: '版本历史',
      icon: 'view',
      clickFn: handleViewHistory
    })
  }

  if (['5', '17'].includes(row.statusCode)) {
    buttons.unshift(
      ...[
        {
          label: '编辑',
          icon: 'edit',
          clickFn: edit
        },
        {
          label: '作废',
          icon: 'cancelApproval',
          clickFn: handleCancel
        }
      ]
    )
  }

  if (row.statusCode === '5') {
    buttons.unshift({
      label: '发布',
      icon: 'uims-icon-commit',
      clickFn: confirmPublish
    })
  }

  if (['1', '2'].includes(row.realChangeStatusCode)) {
    buttons.unshift({
      label: '撤销变更',
      icon: 'goback',
      clickFn: handleRevoke
    })
  }

  if (['7', '11', '16'].includes(row.statusCode)) {
    buttons.unshift({
      label: '变更',
      icon: 'captcha',
      clickFn: change
    })
  }

  return {
    row,
    buttons,
    showNums: 4
  }
}

// 查看
const handleDetail = (row) => {
  addDrawerRef.value.handleOpen('publishView', row)
}

// 变更
const change = (row) => {
  addDrawerRef.value.handleOpen('publishChange', row)
}

const edit = (row) => {
  addDrawerRef.value.handleOpen('publishEdit', row)
}

const handleHistoryDetail = (row) => {
  addDrawerRef.value.handleOpen('historyView', row)
}

const bacthConfirmPublish = () => {
  proxy
    .$confirm('是否发布选中的数据目录？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(async () => {
      const { code, message } = await modelCatalogPublish({
        catalogIds: selectTableRow.value.map((item) => item.cataId)
      })
      if (code === '200') {
        proxy.$message.success('发布成功')
        handleFilter()
      } else {
        proxy.$message.info(message)
        // handleFilter()
      }
    })
}

const handleInport = () => {
  proxy.$refs.uploadFile.click()
}

const handleInportService = (param) => {
  return new Promise(async (resolve, reject) => {
    const _file = param.file
    let formData = {}
    formData.file = _file
    formData.fileName = _file.name
    try {
      const res = await importPublishCatalog(formData)
      resolve(res)
    } catch (err) {
      // this.$message.error(err)
      reject(err)
    }
  })
}

const handleUploadSuccess = (response, file, fileList) => {
  if (response.code === '200') {
    proxy.$message.success(response.message)
    queryParams.currentPage = 1
    getList()
    // formState.serviceMethods.push(...response.data);
  } else {
    proxy.$message.info(response.message)
  }
}

const beforeUpload = (file) => {
  const isLt40M = file.size / 1024 / 1024 < 40 // 其他单个文件大小不超过40MB
  const isPngOrJpg = [
    // "application/vnd.ms-excel",
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ].includes(file.type)
  if (!isPngOrJpg) {
    proxy.$message.info('只允许上传xlsx格式')
  }
  if (!isLt40M) {
    proxy.$message.info('文件上传大小不能超过 40MB!')
  }
  return isLt40M && isPngOrJpg
}

const handleDownload = () => {
  publishCatalogTemplateDownload().then((res) => {
    FileUtils.fileDownload([res], '政务数据目录模板.xlsx')
  })
}

const confirmPublish = async (row) => {
  proxy
    .$confirm('是否发布选中的数据目录？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(async () => {
      try {
        loading.value = true
        const { code, message } = await modelCatalogPublish({
          catalogIds: [row.cataId]
        })
        if (code === '200') {
          proxy.$message.success('发布成功')
          handleFilter()
        } else {
          proxy.$message.info(message)
          // handleFilter()
        }
      } catch (e) {
        loading.value = false
      }
    })
}

const handleCancel = (row) => {
  proxy
    .$confirm('是否作废选中的数据目录？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(async () => {
      const { code, message } = await cancelDataCatalog({
        cataId: row.cataId
      })
      if (code === '200') {
        proxy.$message.success('作废成功')
        handleFilter()
      } else {
        proxy.$message.info(message)
        // handleFilter()
      }
    })
}
const handleHeaderDrag = () => {
  nextTick(() => {
    tableRef.value.doLayout()
  })
}

const handleSizeChange = (val) => {
  queryParams.pageSize = val
  getList()
}
const handleCurrentChange = (val) => {
  queryParams.currentPage = val
  getList()
}

const handleFilter = () => {
  queryParams.currentPage = 1
  nextTick(() => {
    getList()
  })
}

const resetFilter = () => {
  queryParams.status = ''
  queryParams.date = []
  queryParams.directoryName = ''
  queryParams.systemId = ''
  queryParams.relDataCatalogFlg = ''
  getList()
}

const loading = ref(false)
const getList = async () => {
  try {
    loading.value = true
    const params = Object.assign({}, queryParams)
    if (params.date && params.date.length) {
      params.publishTimeBegin = params.date[0]
      params.publishTimeEnd = params.date[1]
    } else {
      params.publishTimeBegin = ''
      params.publishTimeEnd = ''
    }
    if (params.flag === '2') {
      params.status = '3'
    }
    delete params.date
    const { code, data, message } =
      queryParams.flag === '1'
        ? await queryPublishModelCatalogList(params)
        : await querySupplyList(params)

    if (code === '200' || code === 200) {
      results.total = data.total
      results.records = data.records
    } else {
      proxy.$message.error(message)
    }
    handleHeaderDrag()
    loading.value = false
  } catch (e) {
    console.log('e', e)
    loading.value = false
  }
}
const spaceId = ref('')
const getSpaceId = async () => {
  spaceId.value = config.spaceId
  proxy.$store.commit('setSpaceId', spaceId.value)
  let spaceIdCookies = {}
  spaceIdCookies[proxy.$store.state.user.currentUser.userName] = spaceId.value
  Cookies.set('spaceId', JSON.stringify(spaceIdCookies))
  // sessionStorage.setItem('spaceId', spaceId.value)
}
onActivated(() => {
  getSpaceId()
  getList()
  remoteMethod()
})

// 打开选择建模系统弹窗
const moduleSystemRef = ref(null)
const openChooseModuleSystem = (val, id) => {
  moduleSystemRef.value.open(val, id)
}

// 融合新建模型新建模
const addModelByCurrentRef = ref(null)

// 打开新增 物理表
const addModelTableVisible = ref(false)
const openAddModelTableDialog = (row) => {
  addModelTableVisible.value = true
  nextTick(() => {
    proxy.$refs.addModelTableRef.refreshIframeSrc(row)
  })
}

// 打开新增系统
const addSystemDrawerRef = ref(null)
const openAddSystemDrawer = () => {
  addSystemDrawerRef.value.open('add')
}

// 获取来源系统下拉
const getPassStatus = (val) => {
  addDrawerRef.value.getPassStatus(val)
}
// 信息目录/关联分类选择返回
const directoryBack = async (type, info) => {
  if (type === 1) {
    if (info === 'clearUp') {
      addDrawerRef.value.formState.categoryName =
        addDrawerRef.valueformState.categoryId =
        addDrawerRef.value.formState.resourceCode =
          ''
    } else if (info.id && info.id !== addDrawerRef.value.formState.categoryId) {
      const { code, data } =
        await proxy.$api.bizApi.governmentInformationRegister.createCategoryCode(
          info.id
        )
      if (code === '200' || code === 200) {
        addDrawerRef.value.formState.resourceCode = data
      }

      addDrawerRef.value.formState.categoryName = info.name
      addDrawerRef.value.formState.categoryId = info.id
    }
  }
}

// 打开关联分类回调
const relateClassifyDrawerRef = ref(null)
const checkedKeys = ref('')
const openRelateClassifyDrawerCallback = (val, val2, val3) => {
  relateClassifyDrawerRef.value.showWithDupParam(val, '1', val3)
}

const initModelTableCallback = () => {
  addDrawerRef.value.visible && addDrawerRef.value.updateTreeList()
}

const handlePublish = () => {
  addDrawerRef.value.handleOpen('publish', {
    catalogGroup: '2'
  })
}
// const handleCommand = (command) => {
//   addDrawerRef.value.handleOpen('publish', {
//     catalogGroup: command === 'a' ? '2' : '0'
//   })
// }

const editInfo = (type, val, systemId) => {
  if (type === '1') {
    moduleSystemRef.value.open('database', systemId, val)
  } else {
    addModelByCurrentRef.value.handleOpen('dataCatalog', val)
  }
}

// 选择数据库目录下一步
const handleNext = (val) => {
  // console.log('val', val)

  addDrawerRef.value.handleRelateModels(
    val.map((item) => item.cataId),
    'table'
  )
}

// 融合目录下一步
const nextStep = (data) => {
  addDrawerRef.value.virtualTreeList = [
    {
      id: data.modelId,
      name: '融合资源',
      type: 'model',
      children: [
        {
          id: data.entityId,
          name: data.entityName,
          entityId: data.entityId,
          enetityCode: data.enetityCode,
          entityType: '虚拟表',
          entityTypeCode: 'table',
          innerCode: data.innerCode,
          type: 'table',
          tableId: addDrawerRef.value.virtualTable,
          tableResType: data.tableResType,
          isRequest: true,
          tableColumnDtoList: data.selectTableRow
        }
      ]
    }
  ]
  addDrawerRef.value.formState.list =
    addDrawerRef.value.virtualTreeList[0].children[0].tableColumnDtoList

  nextTick(() => {
    addDrawerRef.value.inBoxTree.setChecked(data.entityId, true)
  })
}

const infoItemList = ref([])
const addInfoItemDrawerRef = ref(null)
const openAddInfoItemDrawerCallback = (type, row) => {
  nextTick(() => {
    infoItemList.value = addDrawerRef.value.formState.list
    if (type === 'add') {
      addInfoItemDrawerRef.value.open('add')
    } else {
      addInfoItemDrawerRef.value.open('edit', row)
    }
  })
}

const itemsAE = (info, currentRow) => {
  const flag = addDrawerRef.value.formState.list
    .filter((val) => val.id !== info.id)
    .every((item) => item.itemName !== info.itemName)
  if (!flag) {
    proxy.$message.info('信息项名称重复，请重新添加')
    return
  }
  if (currentRow) {
    const index = addDrawerRef.value.formState.list.findIndex(
      (item) => item.id === currentRow.id
    )
    if (index !== -1) {
      addDrawerRef.value.formState.list.splice(index, 1, info)
      if (!info.id || info.id?.includes('Add')) {
        // 新增的信息项进行变更保存
        addDrawerRef.value.formState.addItemList.forEach((ele) => {
          if (ele.id === info.id) {
            ele = info
          }
        })
      } else {
        // 编辑的信息项
        addDrawerRef.value.formState.editCatalogItems.push({ ...info })
      }
    }
  } else {
    /* eslint-disable */
    addDrawerRef.value.formState.list.push({
      ...info,
      sort: addDrawerRef.value.formState.list.length
        ? addDrawerRef.value.formState.list[
            addDrawerRef.value.formState.list.length - 1
          ].sort + 1
        : 0
    })

    // 新增的信息项
    addDrawerRef.value.formState.addItemList.push({
      ...info,
      sort: addDrawerRef.value.formState.list.length
        ? addDrawerRef.value.formState.list[
            addDrawerRef.value.formState.list.length - 1
          ].sort + 1
        : 0
    })
  }
}

const systemOptions = ref([])
const systemLoading = ref(false)
const remoteMethod = async () => {
  systemLoading.value = true
  const { code, data } =
    await proxy.$api.bizApi.governmentInformationRegister.getAllSystemList({
      // deptId: proxy.$store.state.user.currentUser.unitId
    })
  if (code === '200' || code === 200) {
    let list = data.sort((a, b) => {
      if (a.enableFlag === '1' && b.enableFlag !== '1') {
        return -1
      } else if (a.enableFlag !== '1' && b.enableFlag === '1') {
        return 1
      } else {
        return 0
      }
    })
    systemOptions.value = list
  }
  systemLoading.value = false
}

const tabChange = () => {
  selectTableRow.value = []
  queryParams.currentPage = 1
  results.records = []
  getList()
}

// 快速发布数据库目录
const publishDatabaseCatlaog = (row) => {
  addDrawerRef.value.handleOpen('publish', {
    catalogGroup: '2',
    resourceProviderId: row.resourceProviderId
  })
  addDrawerRef.value.isShowAddBtn = false
  addDrawerRef.value.isShowCatalogTypeSwitch = false
  nextTick(() => {
    addDrawerRef.value.handleDetail(row)
  })
}

// 版本历史弹窗
const historyVersionVisible = ref(false)
const rowCataId = ref('')
// 打开版本历史弹窗
const handleViewHistory = (row) => {
  rowCataId.value = row.cataId
  historyVersionVisible.value = true
}

const handleRevoke = (row) => {
  proxy
    .$confirm('是否撤销变更选中的数据目录？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    .then(async () => {
      try {
        loading.value = true
        const { code, message } = await revokeModelCatalog(row.cataId)
        if (code === '200') {
          proxy.$message.success('撤销成功')
          handleFilter()
        } else {
          proxy.$message.info(message)
          // handleFilter()
        }
      } catch (e) {
        loading.value = false
      }
    })
}

const cataId = ref('')
const gobackReasonDialogVisible = ref(false)
// 目录变更选择撤销发布时打开资源列表
const handleOpenResourceList = (id) => {
  gobackReasonDialogVisible.value = true
  cataId.value = id
}

const confirmCallback = () => {
  addDrawerRef.value.handlePublish('2', false)
}

const orderdResListRef = ref(null)
const openOrderDialog = (data) => {
  orderdResListRef.value.open(data)
}

const gobackCheckStatus = (id, isEntityOrFied, colNeedInfo) => {
  if (isEntityOrFied === '1') {
    addDrawerRef.value.setTreeChecked(id)
  } else {
    addDrawerRef.value.setRowIsShare(colNeedInfo)
  }
}
// 导出
const handleExportRes = async () => {
  const params = new FormData()
  if (queryParams.flag === '1') {
    Object.keys(queryParams)
      .filter(
        (key) => !['currentPage', 'pageSize', 'isPublish', 'flag'].includes(key)
      )
      .forEach((key) => {
        params.append(key, queryParams[key])
      })
    if (
      Object.keys(queryParams)
        .filter(
          (key) =>
            !['currentPage', 'pageSize', 'isPublish', 'flag'].includes(key)
        )
        .every((key) => isEmpty(queryParams[key]))
    ) {
      proxy
        .$confirm('是否导出全部数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
        .then(async () => {
          const res = await exportFgCatalog(queryParams)
          FileUtils.fileDownload(
            [res],
            `数据目录_${moment(new Date()).format('YYYY-MM-DD')}.xlsx`
          )
        })
    } else {
      const res = await exportFgCatalog(queryParams)
      FileUtils.fileDownload(
        [res],
        `数据目录_${moment(new Date()).format('YYYY-MM-DD')}.xlsx`
      )
    }
  } else {
    params.append('status', '3')
    params.append('catalogGroup', '1')
    Object.keys(queryParams)
      .filter((key) => ['directoryName', 'systemId'].includes(key))
      .forEach((key) => {
        params.append(key, queryParams[key])
      })
    if (
      Object.keys(queryParams)
        .filter((key) => ['directoryName', 'systemId'].includes(key))
        .every((key) => isEmpty(queryParams[key]))
    ) {
      proxy
        .$confirm('是否导出全部数据？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
        .then(async () => {
          const res = await exportFgCatalog(queryParams)
          FileUtils.fileDownload(
            [res],
            `快速发布数据库目录_${moment(new Date()).format('YYYY-MM-DD')}.xlsx`
          )
        })
    } else {
      const res = await exportFgCatalog(queryParams)
      FileUtils.fileDownload(
        [res],
        `快速发布数据库目录_${moment(new Date()).format('YYYY-MM-DD')}.xlsx`
      )
    }
  }
}
</script>
