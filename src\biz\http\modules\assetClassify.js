import axios from '@/core/http/axios'
/**  资产分类管理 */

export const addAssetsClassify = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/assetType/add`,
    method: 'post',
    data,
    headers: {
      'X-resource-code': resourceCode
    }
  })
}

export const editAssetsClassify = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/assetType/edit`,
    method: 'post',
    data,
    headers: {
      'X-resource-code': resourceCode
    }
  })
}

export const assetClassifyList = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/assetType/list`,
    method: 'get',
    params: data,
    headers: {
      'X-resource-code': resourceCode
    }
  })
}

export const assetsClassifyTree = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/assetType/tree`,
    method: 'get',
    params: data,
    headers: {
      'X-resource-code': resourceCode
    }
  })
}

export const assetsClassifyOnOrOffline = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/assetType/onOrOffline`,
    method: 'post',
    data,
    headers: {
      'X-resource-code': resourceCode
    }
  })
}

export const deleteAssetsClassify = (data, resourceCode) => {
  return axios({
    url: `/${config.appCode}/assetType/delete`,
    method: 'post',
    data,
    headers: {
      'X-resource-code': resourceCode
    }
  })
}

export const generateCode = (data) => {
  console.log('data', data)

  return axios({
    url: `/${config.appCode}/assetType/genCode`,
    method: 'post',
    data
  })
}
