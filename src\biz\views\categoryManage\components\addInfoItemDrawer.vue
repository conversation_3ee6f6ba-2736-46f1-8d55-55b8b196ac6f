<template>
  <TDrawer
    :title="title"
    :visible.sync="show"
    size="35%"
    @close="cancel"
    class="infoItem"
    :isDestroyWrapper="false"
  >
    <el-form
      ref="form"
      :rules="rules"
      :model="form"
      size="small"
      label-position="right"
      label-width="130px"
    >
      <el-form-item label="信息项名称" prop="itemName">
        <el-input
          v-model.trim="form.itemName"
          clearable
          placeholder="请输入信息项名称"
          :maxlength="100"
        />
      </el-form-item>
      <el-form-item label="数据类型" prop="dataType">
        <select-plus
          style="width: 100%"
          dictType="BM_DATA_TYPE"
          v-model.trim="form.dataType"
          clearable
          @change="dataTypeChange"
          placeholder="请选择数据类型"
        ></select-plus>
      </el-form-item>
      <el-form-item label="数据长度" prop="dataLength" v-if="isShowLength">
        <el-input-number
          v-model.trim="form.dataLength"
          controls-position="right"
          :precision="0"
          :min="1"
          :max="maxlength"
        ></el-input-number>
      </el-form-item>

      <el-form-item
        label="精度"
        prop="accuracy"
        v-if="form.dataType === '9' || form.dataType === '11'"
      >
        <el-input-number
          v-model.trim="form.accuracy"
          controls-position="right"
          :precision="0"
          :min="1"
          :max="38"
        ></el-input-number>
      </el-form-item>
      <el-form-item
        label="时间类型"
        prop="timeType"
        v-if="form.dataType === '4' || form.dataType === '5'"
      >
        <select-plus
          style="width: 100%"
          dictType="BM_TIME_TYPE"
          v-model="form.timeType"
          clearable
          @change="timeTypeChange"
          placeholder="请选择时间类型"
        ></select-plus>
      </el-form-item>
      <el-form-item label="是否向社会开放" prop="isPublic">
        <select-plus
          style="width: 100%"
          dictType="ZYML_IS_FLAG"
          v-model.trim="form.isPublic"
          clearable
          @change="isPublicChange"
          placeholder="请选择是否向社会开放"
        ></select-plus>
      </el-form-item>
      <el-form-item label="数据敏感级别" prop="sensitivityLevel">
        <el-select
          v-model.trim="form.sensitivityLevel"
          placeholder="请选择数据敏感级别"
          filterable
          clearable
          @change="sensitivityLevelChange"
          style="width: 100%"
        >
          <el-tooltip
            effect="light"
            placement="top-end"
            v-for="item in sensitivityList"
            :key="item.value"
            :content="sensitivityContent(item.value)"
          >
            <el-option :label="item.label" :value="item.value"> </el-option>
          </el-tooltip>
        </el-select>
      </el-form-item>
      <el-form-item label="信息项描述" prop="itemDesc">
        <el-input
          placeholder="请输入信息项描述"
          v-model.trim="form.itemDesc"
          type="textarea"
          :autosize="{ minRows: 6, maxRows: 6 }"
          :maxlength="150"
          show-word-limit
        />
      </el-form-item>
      <!-- <el-form-item label="是否向社会开放" prop="isPublic">
        <el-radio-group v-model="form.isPublic">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button size="mini" class="cancel-btn" @click="cancel">
        取消
      </el-button>
      <el-button size="mini" class="save-btn" type="primary" @click="submit">
        确定
      </el-button>
    </template>
  </TDrawer>
</template>

<script>
import SelectPlus from '@/core/components/SelectPlus'
import TDrawer from 'biz/components/common/t-drawer'
import { formatWithSeperator } from '@/core/utils/datetime'

export default {
  name: 'AddInfoItemDrawer',
  components: { SelectPlus, TDrawer },
  props: {
    currentList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '',
      type: '',
      currentRow: '',
      show: false,
      okLoading: false,
      categoryParent: '',
      maxlength: 9999,
      beforeSensitivity: '',
      sensitivityList: [],
      form: {
        id: '',
        itemName: '',
        dataType: '',
        sensitivityLevel: '',
        accuracy: '',
        dataLength: '',
        timeType: '',
        dataTypeCode: '',
        timeTypeCode: '',
        itemDesc: '',
        sensitivityLevelCode: '',
        isPublic: '',
        isPublicCode: '',
        createdTime: '',
        lastUpdatedTime: ''
      },
      rules: {
        itemName: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请输入信息项名称'))
              } else {
                let filterEnd = this.currentList.filter(
                  (item) => item.id !== this.form.id
                )
                if (filterEnd.some((item) => item.itemName === value)) {
                  callback(new Error('检测到信息项名称重复,请重新输入'))
                } else {
                  callback()
                }
              }
            }
          }
        ],
        sensitivityLevel: [
          { required: false, message: '请选择数据敏感级别', trigger: 'change' }
        ],
        dataType: [
          { required: true, message: '请选择数据类型', trigger: 'change' }
        ],
        dataLength: [
          { required: true, message: '请输入数据长度', trigger: 'change' }
        ],
        accuracy: [
          { required: true, message: '请输入精度', trigger: 'change' }
        ],
        isPublic: [
          { required: true, message: '请选择是否向社会开放', trigger: 'change' }
        ],
        //  itemDesc: [{required: true, message: '请输入信息项描述', trigger: 'change'}],
        timeType: [
          { required: true, message: '请选择时间类型', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    isShowLength() {
      let _this = this
      let type = _this.form.dataType
      if (!type || !_this.show) return false
      if (type !== '4' && type !== '5' && type !== '9' && type !== '11') {
        return true
      } else {
        return false
      }
    }
  },
  created() {
    // 获取数据敏感级别
    this.getSensitivityLevel()
  },
  methods: {
    getSensitivityLevel() {
      this.$api.dict
        .getDictByType({ type: 'BM_SENSITIVITY_LEVEL' })
        .then((res) => {
          if (res.code === '200') {
            this.beforeSensitivity = res.data
            const arr = Object.entries(res.data)
            let result = []
            arr.forEach((item) => {
              result.push({ value: item[0], label: item[1] })
            })
            this.sensitivityList = result
          }
        })
    },
    sensitivityContent(val) {
      let title = ''
      if (val === '1') {
        title = '数据泄露后无危害'
      } else if (val === '2') {
        title =
          '数据泄露后无危害，仅对特定公众和群体有益且可能对其他公众和群体产生不利影响'
      } else if (val === '3') {
        title = '数据泄露后会对个人、法人、其他组织或国家机关正常运作造成损害'
      } else if (val === '4') {
        title =
          '数据泄漏后会对个人人身安全、法人正常运作或国家机关正常运作造成严重损害'
      }
      return title
    },
    dataTypeChange(val) {
      if (val) {
        // console.log('this.form', this.form)
        this.form.dataLength = 0
        if (
          val[0] !== '4' &&
          val[0] !== '5' &&
          val[0] !== '9' &&
          val[0] !== '11'
        ) {
          this.form.timeType = ''
          this.form.timeTypeCode = ''
          this.form.accuracy = 0
        } else {
          if (val[0] === '4' || val[0] === '5') {
            this.form.accuracy = 0
          } else {
            this.form.timeType = ''
            this.form.timeTypeCode = ''
          }
        }
        if (val[0] === '2' || val[0] === '3' || val[0] === '10') {
          this.maxlength = 99
        } else {
          this.maxlength = 9999
        }
        this.form.dataTypeCode = val[1]
      } else {
        this.form.dataTypeCode = ''
      }
    },
    isPublicChange(val) {
      if (val) {
        this.form.isPublicCode = val[1]
      } else {
        this.form.isPublicCode = ''
      }
    },
    timeTypeChange(val) {
      if (val) {
        this.form.timeTypeCode = val[1]
      } else {
        this.form.timeTypeCode = ''
      }
    },
    sensitivityLevelChange(val) {
      if (val) {
        this.form.sensitivityLevelCode = this.beforeSensitivity[val]
      } else {
        this.form.sensitivityLevelCode = ''
      }
    },
    cancel() {
      this.$refs.form.clearValidate()
      this.show = false
      this.form = {
        id: '',
        itemName: '',
        dataType: '',
        sensitivityLevel: '',
        accuracy: '',
        dataLength: '',
        timeType: '',
        timeTypeCode: '',
        dataTypeCode: '',
        itemDesc: '',
        sensitivityLevelCode: '',
        isPublic: '',
        isPublicCode: '',
        createdTime: '',
        lastUpdatedTime: '',
        sort: 0
      }
    },
    open(type, row) {
      this.type = type
      if (type === 'add') {
        this.title = '新增信息项'
        this.currentRow = ''
      } else if (type === 'edit') {
        this.title = '编辑信息项'
        this.currentRow = row
        for (const k in this.form) {
          this.form[k] = row[k]
        }
        // console.log('this.form', this.form, row)
        // this.form.dataType = row.row.dataTypeCode;
        // this.form.isPublic = row.row.isPublicCode;
      }
      this.show = true
    },
    async submit() {
      await this.$refs['form'].validate()
      if (this.type === 'add') {
        this.form.id = 'Add' + new Date().getTime()
      }
      let types = this.form.dataType
      if (types !== '4' && types !== '5' && types !== '9' && types !== '11') {
        this.form.timeType = ''
        this.form.timeTypeCode = ''
        this.form.accuracy = ''
      } else {
        this.form.dataLength = ''
        if (types === '4' || types === '5') {
          this.form.accuracy = ''
        } else {
          this.form.timeType = ''
          this.form.timeTypeCode = ''
        }
      }
      let form = Object.assign({}, this.form)
      if (this.type === 'add') {
        form.createdTime = formatWithSeperator(new Date(), '-', ':')
      } else {
        form.lastUpdatedTime = formatWithSeperator(new Date(), '-', ':')
      }
      this.$emit('itemsAE', form, this.currentRow)
      this.cancel()
    }
  }
}
</script>

<style lang="scss" scoped>
.infoItem {
  /deep/ .el-drawer {
    min-width: 488px;
  }
}
</style>
